/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var k=Object.defineProperty;var O=Object.getOwnPropertyDescriptor;var T=Object.getOwnPropertyNames;var N=Object.prototype.hasOwnProperty;var b=(K,i)=>{for(var A in i)k(K,A,{get:i[A],enumerable:!0})},Z=(K,i,A,e)=>{if(i&&typeof i=="object"||typeof i=="function")for(let t of T(i))!N.call(K,t)&&t!==A&&k(K,t,{get:()=>i[t],enumerable:!(e=O(i,t))||e.enumerable});return K};var X=K=>Z(k({},"__esModule",{value:!0}),K);var $={};b($,{default:()=>W});module.exports=X($);var M=require("obsidian");var q={BASE_URL:"https://open.feishu.cn/open-apis",AUTHORIZE_URL:"https://open.feishu.cn/open-apis/authen/v1/authorize",TOKEN_URL:"https://open.feishu.cn/open-apis/authen/v1/access_token",REFRESH_TOKEN_URL:"https://open.feishu.cn/open-apis/authen/v1/refresh_access_token",SCOPES:"contact:user.base:readonly docx:document drive:drive",UPLOAD_URL:"https://open.feishu.cn/open-apis/drive/v1/files/upload_all",DOC_CREATE_URL:"https://open.feishu.cn/open-apis/docx/v1/documents",FOLDER_LIST_URL:"https://open.feishu.cn/open-apis/drive/v1/files",USER_INFO_URL:"https://open.feishu.cn/open-apis/authen/v1/user_info"},H={appId:"",appSecret:"",callbackUrl:"https://md2feishu.xinqi.life/oauth-callback",accessToken:"",refreshToken:"",userInfo:null,defaultFolderId:"",defaultFolderName:"\u6211\u7684\u7A7A\u95F4",titleSource:"filename",frontMatterHandling:"remove",enableLinkShare:!0,linkSharePermission:"anyone_readable",enableSubDocumentUpload:!0,enableLocalImageUpload:!0,enableLocalAttachmentUpload:!0,enableShareMarkInFrontMatter:!0},u={1061002:"\u53C2\u6570\u9519\u8BEF\uFF0C\u8BF7\u68C0\u67E5\u6587\u4EF6\u683C\u5F0F\u548C\u5927\u5C0F",1061005:"\u6587\u4EF6\u5927\u5C0F\u8D85\u51FA\u9650\u5236",1061006:"\u6587\u4EF6\u7C7B\u578B\u4E0D\u652F\u6301",99991663:"access_token \u65E0\u6548",99991664:"access_token \u5DF2\u8FC7\u671F",99991665:"refresh_token \u65E0\u6548",99991666:"refresh_token \u5DF2\u8FC7\u671F"},d={note:{emoji:"\u{1F4DD}",color:"blue",title:"\u7B14\u8BB0"},info:{emoji:"\u2139\uFE0F",color:"blue",title:"\u4FE1\u606F"},tip:{emoji:"\u{1F4A1}",color:"green",title:"\u63D0\u793A"},hint:{emoji:"\u{1F4A1}",color:"green",title:"\u63D0\u793A"},warning:{emoji:"\u26A0\uFE0F",color:"yellow",title:"\u8B66\u544A"},caution:{emoji:"\u26A0\uFE0F",color:"yellow",title:"\u6CE8\u610F"},attention:{emoji:"\u26A0\uFE0F",color:"yellow",title:"\u6CE8\u610F"},error:{emoji:"\u274C",color:"red",title:"\u9519\u8BEF"},danger:{emoji:"\u26D4",color:"red",title:"\u5371\u9669"},failure:{emoji:"\u274C",color:"red",title:"\u5931\u8D25"},fail:{emoji:"\u274C",color:"red",title:"\u5931\u8D25"},missing:{emoji:"\u2753",color:"red",title:"\u7F3A\u5931"},success:{emoji:"\u2705",color:"green",title:"\u6210\u529F"},check:{emoji:"\u2705",color:"green",title:"\u68C0\u67E5"},done:{emoji:"\u2705",color:"green",title:"\u5B8C\u6210"},question:{emoji:"\u2753",color:"purple",title:"\u95EE\u9898"},help:{emoji:"\u2753",color:"purple",title:"\u5E2E\u52A9"},faq:{emoji:"\u2753",color:"purple",title:"\u5E38\u89C1\u95EE\u9898"},quote:{emoji:"\u{1F4AC}",color:"gray",title:"\u5F15\u7528"},cite:{emoji:"\u{1F4D6}",color:"gray",title:"\u5F15\u7528"},abstract:{emoji:"\u{1F4C4}",color:"cyan",title:"\u6458\u8981"},summary:{emoji:"\u{1F4C4}",color:"cyan",title:"\u603B\u7ED3"},tldr:{emoji:"\u{1F4C4}",color:"cyan",title:"TL;DR"},example:{emoji:"\u{1F4CB}",color:"purple",title:"\u793A\u4F8B"},todo:{emoji:"\u2611\uFE0F",color:"blue",title:"\u5F85\u529E"},default:{emoji:"\u{1F4CC}",color:"blue",title:"\u63D0\u793A"}};var V=require("obsidian");var r=class{static log(...i){this.enabled&&console.log("[Feishu]",...i)}static warn(...i){this.enabled&&console.warn("[Feishu]",...i)}static error(...i){this.enabled&&console.error("[Feishu]",...i)}static enable(){this.enabled=!0}static disable(){this.enabled=!1}static isEnabled(){return this.enabled}};r.enabled=!1;var w=class{constructor(i,A){this.settings=i,this.app=A}updateSettings(i){this.settings=i}generateAuthUrl(){if(!this.settings.appId||!this.settings.appSecret)throw new Error("\u8BF7\u5148\u5728\u8BBE\u7F6E\u4E2D\u914D\u7F6E\u98DE\u4E66\u5E94\u7528\u7684 App ID \u548C App Secret");let i=this.generateRandomState();localStorage.setItem("feishu-oauth-state",i);let A=this.settings.callbackUrl,e=new URLSearchParams({app_id:this.settings.appId,redirect_uri:A,scope:q.SCOPES,state:i,response_type:"code"});return`${q.AUTHORIZE_URL}?${e.toString()}`}async processCallback(i){try{let A=new URL(i),e=A.searchParams.get("code"),t=A.searchParams.get("state"),o=A.searchParams.get("error");if(o)return r.error("OAuth error:",o),!1;if(!e)return r.error("No authorization code in callback"),!1;let s=localStorage.getItem("feishu-oauth-state");return s&&t!==s?(r.error("State mismatch"),!1):await this.handleOAuthCallback(e)}catch(A){return r.error("Process callback error:",A),!1}}async handleOAuthCallback(i){try{if(!this.settings.appId||!this.settings.appSecret)throw new Error("\u5E94\u7528\u914D\u7F6E\u4E0D\u5B8C\u6574");let A=await this.exchangeCodeForToken(i);if(!A.success)throw new Error(A.error||"\u83B7\u53D6\u8BBF\u95EE\u4EE4\u724C\u5931\u8D25");let e=await this.getUserInfo();if(e)return this.settings.userInfo=e,new V.Notice("\u2705 \u98DE\u4E66\u6388\u6743\u6210\u529F\uFF01"),!0;throw new Error("\u83B7\u53D6\u7528\u6237\u4FE1\u606F\u5931\u8D25")}catch(A){return r.error("OAuth callback error:",A),new V.Notice(`\u274C \u6388\u6743\u5931\u8D25: ${A.message}`),!1}}async exchangeCodeForToken(i){try{let A=await(0,V.requestUrl)({url:"https://open.feishu.cn/open-apis/auth/v3/app_access_token/internal",method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({app_id:this.settings.appId,app_secret:this.settings.appSecret})}),e=A.json||JSON.parse(A.text);if(e.code!==0)return r.error("Failed to get app access token:",e),{success:!1,error:`\u83B7\u53D6\u5E94\u7528\u4EE4\u724C\u5931\u8D25: ${e.msg}`};let t=e.app_access_token,o={grant_type:"authorization_code",code:i},s=await(0,V.requestUrl)({url:q.TOKEN_URL,method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${t}`},body:JSON.stringify(o)}),E;if(s.json&&typeof s.json=="object")E=s.json;else if(s.text){let I=s.text;E=JSON.parse(I)}else r.log("Trying to call response.json()..."),E=await s.json();return E.code===0?(this.settings.accessToken=E.data.access_token,this.settings.refreshToken=E.data.refresh_token,{success:!0}):(r.error("Token exchange failed:",E),{success:!1,error:E.msg})}catch(A){return r.error("Token exchange error:",A),{success:!1,error:A.message}}}async getUserInfo(){try{let i=await(0,V.requestUrl)({url:q.USER_INFO_URL,method:"GET",headers:{Authorization:`Bearer ${this.settings.accessToken}`,"Content-Type":"application/json"}}),A=i.json||JSON.parse(i.text);return A.code===0?{name:A.data.name,avatar_url:A.data.avatar_url,email:A.data.email,user_id:A.data.user_id}:(r.error("Get user info failed:",A),null)}catch(i){return r.error("Get user info error:",i),null}}async shareMarkdownWithFiles(i,A,e){try{if(e&&e.setMessage("\u{1F50D} \u6B63\u5728\u68C0\u67E5\u6388\u6743\u72B6\u6001..."),!await this.ensureValidTokenWithReauth(e))throw new Error("\u6388\u6743\u5931\u6548\u4E14\u91CD\u65B0\u6388\u6743\u5931\u8D25\uFF0C\u8BF7\u624B\u52A8\u91CD\u65B0\u6388\u6743");e&&e.setMessage("\u{1F4E4} \u6B63\u5728\u4E0A\u4F20\u6587\u4EF6\u5230\u98DE\u4E66...");let o=await this.uploadMarkdownFile(i,A.content);if(!o.success)throw new Error(o.error||"\u6587\u4EF6\u4E0A\u4F20\u5931\u8D25");if(!o.fileToken)throw new Error("\u6587\u4EF6\u4E0A\u4F20\u6210\u529F\u4F46\u672A\u83B7\u53D6\u5230\u6587\u4EF6\u4EE4\u724C");let s=o.url||`https://feishu.cn/file/${o.fileToken}`;try{let E=i.endsWith(".md")?i.slice(0,-3):i,I=await this.createImportTaskWithCorrectFolder(o.fileToken,E);if(I.success&&I.ticket){r.log("Step 3: Waiting for import completion (15s timeout)...");let g=await this.waitForImportCompletionWithTimeout(I.ticket,15e3);if(g.success&&g.documentToken){let a=`https://feishu.cn/docx/${g.documentToken}`,n=[];if(this.settings.enableLinkShare&&g.documentToken){let l=(async()=>{try{e&&e.setMessage("\u{1F517} \u6B63\u5728\u8BBE\u7F6E\u6587\u6863\u5206\u4EAB\u6743\u9650..."),await this.setDocumentSharePermissions(g.documentToken,!0),r.log("\u2705 Document share permissions set successfully")}catch(c){r.warn("\u26A0\uFE0F Failed to set document share permissions:",c)}})();n.push(l)}if(n.length>0&&await Promise.allSettled(n),A.localFiles.length>0)try{let l=A.localFiles.filter(Q=>Q.isSubDocument),c=A.localFiles.filter(Q=>!Q.isSubDocument);l.length>0&&(e&&e.setMessage(`\u{1F4C4} \u6B63\u5728\u5904\u7406 ${l.length} \u4E2A\u5B50\u6587\u6863...`),await this.processSubDocuments(g.documentToken,l,e)),c.length>0&&(e&&e.setMessage(`\u{1F4CE} \u6B63\u5728\u5904\u7406 ${c.length} \u4E2A\u9644\u4EF6...`),await this.processFileUploads(g.documentToken,c,e))}catch(l){r.warn("\u26A0\uFE0F File upload processing failed:",l)}try{await this.deleteSourceFile(o.fileToken)}catch(l){r.warn("\u26A0\uFE0F Failed to delete source file:",l.message)}return{success:!0,title:E,url:a}}else return r.warn("\u26A0\uFE0F Import task failed or timed out, falling back to file URL"),r.warn("Final result details:",g),{success:!0,title:i,url:s}}else return r.warn("\u26A0\uFE0F Failed to create import task, falling back to file URL"),r.warn("Import result details:",I),{success:!0,title:i,url:s}}catch(E){return r.warn("\u26A0\uFE0F Import process failed, falling back to file URL:",E.message),r.error("Import error details:",E),{success:!0,title:i,url:s}}}catch(t){return r.error("Share markdown error:",t),{success:!1,error:t.message}}}async shareMarkdown(i,A,e){try{if(e&&e.setMessage("\u{1F50D} \u6B63\u5728\u68C0\u67E5\u6388\u6743\u72B6\u6001..."),!await this.ensureValidTokenWithReauth(e))throw new Error("\u6388\u6743\u5931\u6548\u4E14\u91CD\u65B0\u6388\u6743\u5931\u8D25\uFF0C\u8BF7\u624B\u52A8\u91CD\u65B0\u6388\u6743");e&&e.setMessage("\u{1F4E4} \u6B63\u5728\u4E0A\u4F20\u6587\u4EF6\u5230\u98DE\u4E66...");let o=await this.uploadMarkdownFile(i,A);if(!o.success)throw new Error(o.error||"\u6587\u4EF6\u4E0A\u4F20\u5931\u8D25");if(!o.fileToken)throw new Error("\u6587\u4EF6\u4E0A\u4F20\u6210\u529F\u4F46\u672A\u83B7\u53D6\u5230\u6587\u4EF6\u4EE4\u724C");let s=`https://feishu.cn/file/${o.fileToken}`;e&&e.setMessage("\u{1F504} \u6B63\u5728\u8F6C\u6362\u4E3A\u98DE\u4E66\u6587\u6863...");try{let E=i.endsWith(".md")?i.slice(0,-3):i,I=await this.createImportTaskWithCorrectFolder(o.fileToken,E);if(I.success&&I.ticket){r.log("Step 3: Waiting for import completion (15s timeout)...");let g=await this.waitForImportCompletionWithTimeout(I.ticket,15e3);if(g.success&&g.documentToken){let a=`https://feishu.cn/docx/${g.documentToken}`,n=[];if(this.settings.enableLinkShare&&g.documentToken){let c=(async()=>{try{e&&e.setMessage("\u{1F517} \u6B63\u5728\u8BBE\u7F6E\u6587\u6863\u5206\u4EAB\u6743\u9650..."),await this.setDocumentSharePermissions(g.documentToken,!0),r.log("\u2705 Document share permissions set successfully")}catch(Q){r.warn("\u26A0\uFE0F Failed to set document share permissions:",Q)}})();n.push(c)}let l=(async()=>{try{await this.deleteSourceFile(o.fileToken)}catch(c){r.warn("\u26A0\uFE0F Failed to delete source file:",c)}})();return n.push(l),await Promise.allSettled(n),{success:!0,title:E,url:a}}else return r.warn("\u26A0\uFE0F Import task failed or timed out, falling back to file URL"),r.warn("Final result details:",g),{success:!0,title:i,url:s}}else return r.warn("\u26A0\uFE0F Failed to create import task, falling back to file URL"),r.warn("Import result details:",I),{success:!0,title:i,url:s}}catch(E){return r.warn("\u26A0\uFE0F Import process failed, falling back to file URL:",E.message),r.error("Import error details:",E),{success:!0,title:i,url:s}}}catch(t){return r.error("Share markdown error:",t),{success:!1,error:t.message}}}async getFolderList(i){try{if(!await this.ensureValidToken())throw new Error("Token\u65E0\u6548\uFF0C\u8BF7\u91CD\u65B0\u6388\u6743");let e=`${q.BASE_URL}/drive/v1/files`,t=new URLSearchParams({folder_token:i||"",page_size:"50"}),o=await(0,V.requestUrl)({url:`${e}?${t.toString()}`,method:"GET",headers:{Authorization:`Bearer ${this.settings.accessToken}`,"Content-Type":"application/json"}}),s=o.json||JSON.parse(o.text);if(s.code===0)return{code:0,data:{folders:s.data.files.filter(I=>I.type==="folder").map(I=>({...I,folder_token:I.token,token:I.token})),has_more:s.data.has_more}};throw new Error(s.msg||"\u83B7\u53D6\u6587\u4EF6\u5939\u5217\u8868\u5931\u8D25")}catch(A){throw r.error("Get folder list error:",A),A}}async uploadMarkdownFile(i,A){try{if(!await this.ensureValidToken())throw new Error("Token\u65E0\u6548\uFF0C\u8BF7\u91CD\u65B0\u6388\u6743");let t="---7MA4YWxkTrZu0gW",o=i.endsWith(".md")?i:`${i}.md`,s=new TextEncoder().encode(A),E=s.length,I=[];I.push(`--${t}`),I.push('Content-Disposition: form-data; name="file_name"'),I.push(""),I.push(o),I.push(`--${t}`),I.push('Content-Disposition: form-data; name="parent_type"'),I.push(""),I.push("explorer"),I.push(`--${t}`),I.push('Content-Disposition: form-data; name="size"'),I.push(""),I.push(E.toString()),this.settings.defaultFolderId&&this.settings.defaultFolderId!==""&&this.settings.defaultFolderId!=="nodcn2EG5YG1i5Rsh5uZs0FsUje"&&(I.push(`--${t}`),I.push('Content-Disposition: form-data; name="parent_node"'),I.push(""),I.push(this.settings.defaultFolderId)),I.push(`--${t}`),I.push(`Content-Disposition: form-data; name="file"; filename="${o}"`),I.push("Content-Type: text/markdown"),I.push("");let g=I.join(`\r
`)+`\r
`,a=`\r
--${t}--\r
`,n=new TextEncoder().encode(g),l=new TextEncoder().encode(a),c=n.length+s.length+l.length,Q=new Uint8Array(c),Y=0;Q.set(n,Y),Y+=n.length,Q.set(s,Y),Y+=s.length,Q.set(l,Y);let h=await(0,V.requestUrl)({url:q.UPLOAD_URL,method:"POST",headers:{Authorization:`Bearer ${this.settings.accessToken}`,"Content-Type":`multipart/form-data; boundary=${t}`},body:Q.buffer}),x=h.json||JSON.parse(h.text);if(x.code===0){let G=`https://feishu.cn/file/${x.data.file_token}`;return{success:!0,fileToken:x.data.file_token,url:G}}else{let G=u[x.code]||x.msg||"\u4E0A\u4F20\u5931\u8D25";return r.error("Upload failed:",x),{success:!1,error:G}}}catch(e){return r.error("Upload file error:",e),{success:!1,error:e.message}}}async refreshAccessToken(){try{if(!this.settings.refreshToken)return!1;let i=await(0,V.requestUrl)({url:q.REFRESH_TOKEN_URL,method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({grant_type:"refresh_token",refresh_token:this.settings.refreshToken})}),A=i.json||JSON.parse(i.text);return A.code===0?(this.settings.accessToken=A.data.access_token,this.settings.refreshToken=A.data.refresh_token,!0):(r.error("Token refresh failed:",A),!1)}catch(i){return r.error("Token refresh error:",i),!1}}generateRandomState(){return Math.random().toString(36).substring(2,15)+Math.random().toString(36).substring(2,15)}async ensureValidToken(){if(!this.settings.accessToken)return!1;try{let i=await(0,V.requestUrl)({url:q.USER_INFO_URL,method:"GET",headers:{Authorization:`Bearer ${this.settings.accessToken}`}}),A=i.json||JSON.parse(i.text);return A.code===0?!0:A.code===99991664?await this.refreshAccessToken():!1}catch(i){return r.error("Token validation error:",i),!1}}async ensureValidTokenWithReauth(i){if(!this.settings.accessToken)return await this.triggerReauth("\u6CA1\u6709\u8BBF\u95EE\u4EE4\u724C",i);try{let A=await(0,V.requestUrl)({url:q.USER_INFO_URL,method:"GET",headers:{Authorization:`Bearer ${this.settings.accessToken}`}}),e=A.json||JSON.parse(A.text);return e.code===0?!0:this.isTokenExpiredError(e.code)?await this.refreshAccessToken()?!0:!!await this.triggerReauth("Token\u5237\u65B0\u5931\u8D25",i):!!await this.triggerReauth(`Token\u65E0\u6548 (\u9519\u8BEF\u7801: ${e.code})`,i)}catch(A){return r.error("Token\u9A8C\u8BC1\u51FA\u9519:",A),!!await this.triggerReauth("Token\u9A8C\u8BC1\u51FA\u9519",i)}}isTokenExpiredError(i){return[99991664,99991663,99991665,99991666,1].includes(i)}async triggerReauth(i,A){A?A.setMessage(`\u{1F504} ${i}\uFF0C\u6B63\u5728\u81EA\u52A8\u91CD\u65B0\u6388\u6743...`):new V.Notice(`\u{1F504} ${i}\uFF0C\u6B63\u5728\u81EA\u52A8\u91CD\u65B0\u6388\u6743...`);try{if(!this.settings.appId||!this.settings.appSecret){let t="\u274C \u5E94\u7528\u914D\u7F6E\u4E0D\u5B8C\u6574\uFF0C\u8BF7\u5728\u8BBE\u7F6E\u4E2D\u914D\u7F6E App ID \u548C App Secret";return A?(A.setMessage(t),setTimeout(()=>A.hide(),3e3)):new V.Notice(t),!1}let e=this.generateAuthUrl();return window.open(e,"_blank"),A?A.setMessage("\u{1F310} \u5DF2\u6253\u5F00\u6D4F\u89C8\u5668\u8FDB\u884C\u91CD\u65B0\u6388\u6743\uFF0C\u5B8C\u6210\u540E\u5C06\u81EA\u52A8\u7EE7\u7EED\u5206\u4EAB..."):new V.Notice("\u{1F310} \u5DF2\u6253\u5F00\u6D4F\u89C8\u5668\u8FDB\u884C\u91CD\u65B0\u6388\u6743\uFF0C\u5B8C\u6210\u540E\u5C06\u81EA\u52A8\u7EE7\u7EED\u5206\u4EAB..."),await this.waitForReauth(A)}catch(e){return r.error("\u91CD\u65B0\u6388\u6743\u5931\u8D25:",e),new V.Notice(`\u274C \u91CD\u65B0\u6388\u6743\u5931\u8D25: ${e.message}`),!1}}async waitForReauth(i){return new Promise(A=>{let e=setTimeout(()=>{window.removeEventListener("feishu-auth-success",t);let o="\u23F0 \u6388\u6743\u7B49\u5F85\u8D85\u65F6\uFF0C\u8BF7\u624B\u52A8\u91CD\u8BD5\u5206\u4EAB";i?(i.setMessage(o),setTimeout(()=>i.hide(),3e3)):new V.Notice(o),A(!1)},3e5),t=()=>{clearTimeout(e),window.removeEventListener("feishu-auth-success",t),i&&i.setMessage("\u2705 \u6388\u6743\u6210\u529F\uFF0C\u6B63\u5728\u7EE7\u7EED\u5206\u4EAB..."),setTimeout(()=>{A(!0)},1e3)};window.addEventListener("feishu-auth-success",t)})}async createImportTaskWithCorrectFolder(i,A){try{let e={file_extension:"md",file_token:i,type:"docx",file_name:A,point:{mount_type:1,mount_key:this.settings.defaultFolderId||"nodcn2EG5YG1i5Rsh5uZs0FsUje"}},t=await(0,V.requestUrl)({url:`${q.BASE_URL}/drive/v1/import_tasks`,method:"POST",headers:{Authorization:`Bearer ${this.settings.accessToken}`,"Content-Type":"application/json"},body:JSON.stringify(e)}),o=t.json||JSON.parse(t.text);return o.code===0?{success:!0,ticket:o.data.ticket}:{success:!1,error:o.msg||"\u521B\u5EFA\u5BFC\u5165\u4EFB\u52A1\u5931\u8D25"}}catch(e){return r.error("Create import task error:",e),{success:!1,error:e.message}}}async waitForImportCompletionWithTimeout(i,A){let e=Date.now(),t=25;for(let o=1;o<=t;o++){let s=Date.now()-e;if(s>=A)return r.warn(`Import timeout after ${s}ms`),{success:!1,error:`\u5BFC\u5165\u4EFB\u52A1\u8D85\u65F6 (${A}ms)`};try{let E=await this.checkImportStatus(i);if(E.success&&(E.status===3||E.status===0)){if(E.documentToken)return{success:!0,documentToken:E.documentToken};r.warn("Import completed but no document token returned, continuing to wait...")}else if(E.success&&E.status===2){if(r.log(`\u{1F50D} Status 2 detected. Document token: ${E.documentToken||"none"}`),E.documentToken)return r.log(`\u2705 Import completed despite failure status, got document token: ${E.documentToken}`),{success:!0,documentToken:E.documentToken};if(r.warn(`\u26A0\uFE0F Import shows failure status (${E.status}), no document token yet. Attempt ${o}/8, continuing to wait...`),!(o<=8))return r.error("\u274C Import failed after extended waiting"),{success:!1,error:"\u5BFC\u5165\u4EFB\u52A1\u5931\u8D25"}}else r.log(`\u{1F4CA} Other status: ${E.status}, success: ${E.success}`);if(o<t){let I=this.getDelayForAttempt(o);await new Promise(g=>setTimeout(g,I))}}catch(E){r.error("Check import status error:",E);let I=this.getDelayForAttempt(o);await new Promise(g=>setTimeout(g,I))}}return{success:!1,error:"\u5BFC\u5165\u4EFB\u52A1\u8D85\u65F6"}}getDelayForAttempt(i){return i<=3?1e3:i<=8?2e3:3e3}async checkImportStatus(i){try{let A=await(0,V.requestUrl)({url:`${q.BASE_URL}/drive/v1/import_tasks/${i}`,method:"GET",headers:{Authorization:`Bearer ${this.settings.accessToken}`,"Content-Type":"application/json"}}),e=A.json||JSON.parse(A.text);if(e.code===0){let t=e.data.result;return{success:!0,status:t.job_status,documentToken:t.token}}else return r.error("\u274C Import status check failed:",e),{success:!1,error:e.msg||"\u68C0\u67E5\u5BFC\u5165\u72B6\u6001\u5931\u8D25"}}catch(A){return r.error("Check import status error:",A),{success:!1,error:A.message}}}async deleteSourceFile(i){try{let A;try{A=await(0,V.requestUrl)({url:`${q.BASE_URL}/drive/v1/files/${i}/trash`,method:"POST",headers:{Authorization:`Bearer ${this.settings.accessToken}`,"Content-Type":"application/json"},body:JSON.stringify({})})}catch(t){r.warn("\u26A0\uFE0F Trash method failed, trying direct delete..."),A=await(0,V.requestUrl)({url:`${q.BASE_URL}/drive/v1/files/${i}?type=file`,method:"DELETE",headers:{Authorization:`Bearer ${this.settings.accessToken}`,"Content-Type":"application/json"}})}if(A.status!==200)throw new Error(`\u5220\u9664\u8BF7\u6C42\u5931\u8D25\uFF0C\u72B6\u6001\u7801: ${A.status}`);let e=A.json||JSON.parse(A.text);e.code!==0&&(r.warn("\u26A0\uFE0F Delete API returned non-zero code:",e.code,e.msg),r.log("\u{1F4DD} Source file deletion completed (may have been moved to trash)"))}catch(A){r.error("\u274C Delete source file error:",A)}}async findPlaceholderBlocks(i,A){try{let e=[],t="",o=!0,s=this.compilePlaceholderPatterns(A),E=new Set(A.map(I=>I.placeholder));for(r.log(`\u{1F50D} Searching for ${E.size} placeholders in document...`);o&&E.size>0;){let I=new URLSearchParams({page_size:"500"});t&&I.append("page_token",t);let g=await(0,V.requestUrl)({url:`${q.BASE_URL}/docx/v1/documents/${i}/blocks?${I.toString()}`,method:"GET",headers:{Authorization:`Bearer ${this.settings.accessToken}`,"Content-Type":"application/json"}}),a=g.json||JSON.parse(g.text);if(a.code!==0)throw new Error(a.msg||"\u83B7\u53D6\u6587\u6863\u5757\u5931\u8D25");let n=this.searchPlaceholdersInBlocks(a.data.items,s,E);if(e.push(...n),E.size===0){r.log(`\u2705 All ${A.length} placeholders found, stopping search early`);break}o=a.data.has_more,t=a.data.page_token}return r.log(`\u{1F3AF} Found ${e.length}/${A.length} placeholder blocks`),e}catch(e){throw r.error("Find placeholder blocks error:",e),e}}compilePlaceholderPatterns(i){let A=new Map;return i.forEach(e=>{let t=e.placeholder,o=t.replace(/^__/,"").replace(/__$/,""),s=[new RegExp(this.escapeRegExp(t)),new RegExp(this.escapeRegExp(`!${o}`)),new RegExp(this.escapeRegExp(o))];A.set(t,{fileInfo:e,patterns:s})}),A}searchPlaceholdersInBlocks(i,A,e){let t=[];for(let o=0;o<i.length;o++){let s=i[o];if(!s.text||!s.text.elements)continue;let E=this.extractBlockTextContent(s);if(this.hasPlaceholderFeatures(E))for(let I of e){let g=A.get(I);if(!g)continue;if(g.patterns.some(n=>n.test(E))&&(r.log(`\u2705 Found placeholder: "${I}" in block ${s.block_id}`),t.push({blockId:s.block_id,parentId:s.parent_id,index:o,placeholder:I,fileInfo:g.fileInfo}),e.delete(I),e.size===0))return t}}return t}extractBlockTextContent(i){return i.text.elements.filter(A=>A.text_run&&A.text_run.content).map(A=>A.text_run.content).join("")}hasPlaceholderFeatures(i){return i.includes("FEISHU_FILE_")||i.includes("__FEISHU_FILE_")}escapeRegExp(i){return i.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}async insertFileBlock(i,A){try{let e=A.fileInfo.isImage?27:23,t=A.fileInfo.isImage?{image:{}}:{file:{}},o={index:A.index,children:[{block_type:e,...t}]},s=await(0,V.requestUrl)({url:`${q.BASE_URL}/docx/v1/documents/${i}/blocks/${A.parentId}/children`,method:"POST",headers:{Authorization:`Bearer ${this.settings.accessToken}`,"Content-Type":"application/json"},body:JSON.stringify(o)}),E=s.json||JSON.parse(s.text);if(E.code!==0)throw new Error(E.msg||"\u63D2\u5165\u6587\u4EF6\u5757\u5931\u8D25");let I=E.data.children[0],g=I.block_id;return!A.fileInfo.isImage&&I.block_type===33&&(I.children&&I.children.length>0?g=I.children[0]:r.warn("\u26A0\uFE0F View Block created but no child File Block found")),g}catch(e){throw r.error("Insert file block error:",e),e}}async uploadFileToDocument(i,A,e,t){try{if(!await this.ensureValidToken())throw new Error("Token\u65E0\u6548\uFF0C\u8BF7\u91CD\u65B0\u6388\u6743");let s="---7MA4YWxkTrZu0gW",E=e.isImage?"docx_image":"docx_file",I=t.byteLength,g=[];g.push(`--${s}`),g.push('Content-Disposition: form-data; name="file_name"'),g.push(""),g.push(e.fileName),g.push(`--${s}`),g.push('Content-Disposition: form-data; name="parent_type"'),g.push(""),g.push(E),g.push(`--${s}`),g.push('Content-Disposition: form-data; name="parent_node"'),g.push(""),g.push(A),g.push(`--${s}`),g.push('Content-Disposition: form-data; name="size"'),g.push(""),g.push(I.toString()),g.push(`--${s}`),g.push('Content-Disposition: form-data; name="extra"'),g.push(""),g.push(`{"drive_route_token":"${i}"}`),g.push(`--${s}`),g.push('Content-Disposition: form-data; name="file"'),g.push("Content-Type: application/octet-stream"),g.push("");let a=g.join(`\r
`)+`\r
`,n=`\r
--${s}--\r
`,l=new TextEncoder().encode(a),c=new TextEncoder().encode(n),Q=l.length+I+c.length,Y=new Uint8Array(Q),h=0;Y.set(l,h),h+=l.length,Y.set(new Uint8Array(t),h),h+=I,Y.set(c,h);let x=await(0,V.requestUrl)({url:q.UPLOAD_URL,method:"POST",headers:{Authorization:`Bearer ${this.settings.accessToken}`,"Content-Type":`multipart/form-data; boundary=${s}`},body:Y.buffer}),G=x.json||JSON.parse(x.text);if(G.code===0)return r.log(`\u2705 Uploaded ${e.isImage?"image":"file"} material: ${G.data.file_token}`),G.data.file_token;{let p=u[G.code]||G.msg||"\u4E0A\u4F20\u6587\u4EF6\u7D20\u6750\u5931\u8D25";throw new Error(p)}}catch(o){throw r.error("Upload file to document error:",o),o}}async setFileBlockContent(i,A,e,t){try{let o=t?{replace_image:{token:e}}:{replace_file:{token:e}};r.log(`\u{1F527} Setting ${t?"image":"file"} block content:`,{documentId:i,blockId:A,fileToken:e,requestData:o});let s=await(0,V.requestUrl)({url:`${q.BASE_URL}/docx/v1/documents/${i}/blocks/${A}`,method:"PATCH",headers:{Authorization:`Bearer ${this.settings.accessToken}`,"Content-Type":"application/json"},body:JSON.stringify(o)});r.log(`\u{1F4CB} Set block content response status: ${s.status}`);let E=s.json||JSON.parse(s.text);if(r.log("\u{1F4CB} Set block content response:",E),E.code!==0)throw new Error(E.msg||"\u8BBE\u7F6E\u6587\u4EF6\u5757\u5185\u5BB9\u5931\u8D25");r.log(`\u2705 Set ${t?"image":"file"} block content: ${A}`)}catch(o){throw r.error("Set file block content error:",o),o.message&&o.message.includes("400")&&(r.error("\u274C 400 Error details: This might be due to:"),r.error("  1. Invalid file token or block ID"),r.error("  2. File type not supported for this block type"),r.error("  3. Block already has content"),r.error("  4. API parameter format issue")),o}}async findRemainingPlaceholders(i,A){try{r.log(`\u{1F50D} Checking ${A.length} placeholders for remaining content...`);let e=[],t=new Set,o="",s=!0,E=[];for(;s;){let I=await(0,V.requestUrl)({url:`${q.BASE_URL}/docx/v1/documents/${i}/blocks?page_size=500${o?`&page_token=${o}`:""}`,method:"GET",headers:{Authorization:`Bearer ${this.settings.accessToken}`,"Content-Type":"application/json"}}),g=I.json||JSON.parse(I.text);if(g.code!==0){r.warn("Failed to get document blocks for placeholder check:",g.msg);break}E.push(...g.data.items),s=g.data.has_more,o=g.data.page_token}r.log(`\u{1F4CB} Retrieved ${E.length} blocks from document`);for(let I of A){if(t.has(I.blockId))continue;t.add(I.blockId);let g=E.find(a=>a.block_id===I.blockId);if(g&&g.text){let a=this.extractBlockTextContent(g);r.log(`\u{1F50D} Checking block ${I.blockId}: "${a.substring(0,100)}..."`);let n=I.placeholder,l=n.replace(/^__/,"").replace(/__$/,""),c=`!${l}!`,Q=a.includes(n),Y=a.includes(c),h=a.includes(l);if(Q||Y||h){let x=Q?"original":Y?"feishu":"clean";r.log(`\u2705 Found remaining placeholder: ${n} (format: ${x})`),e.push(I)}else r.log(`\u274C Placeholder already cleaned: ${n}`)}else r.log(`\u26A0\uFE0F Block not found or has no text: ${I.blockId}`)}return r.log(`\u{1F3AF} Found ${e.length} remaining placeholders out of ${A.length}`),e}catch(e){return r.error("Error finding remaining placeholders:",e),r.log("\u{1F504} Falling back to processing all placeholders due to error"),A}}async batchReplacePlaceholderText(i,A){if(A.length!==0)try{r.log(`\u{1F527} Batch replacing ${A.length} placeholder texts...`);let t={requests:A.map(E=>({block_id:E.blockId,update_text_elements:{elements:[{text_run:{content:""}}]}}))},o=await(0,V.requestUrl)({url:`${q.BASE_URL}/docx/v1/documents/${i}/blocks/batch_update`,method:"PATCH",headers:{Authorization:`Bearer ${this.settings.accessToken}`,"Content-Type":"application/json"},body:JSON.stringify(t)}),s=o.json||JSON.parse(o.text);r.log("\u{1F4CB} Batch replace placeholder response:",s),s.code!==0?(r.warn(`\u26A0\uFE0F Batch replace failed: ${s.msg}, falling back to individual replacement...`),await this.fallbackIndividualReplace(i,A)):r.log(`\u2705 Successfully batch replaced ${A.length} placeholder texts`)}catch(e){r.error("Batch replace placeholder text error:",e),await this.fallbackIndividualReplace(i,A)}}async fallbackIndividualReplace(i,A){r.log(`\u{1F504} Falling back to individual replacement for ${A.length} blocks...`);for(let e of A)try{await this.replacePlaceholderText(i,e)}catch(t){r.error(`\u274C Failed to replace placeholder ${e.blockId}:`,t)}}async replacePlaceholderText(i,A){try{let e={update_text_elements:{elements:[{text_run:{content:""}}]}};r.log(`\u{1F527} Replacing placeholder text in block: ${A.blockId}`);let t=await(0,V.requestUrl)({url:`${q.BASE_URL}/docx/v1/documents/${i}/blocks/${A.blockId}`,method:"PATCH",headers:{Authorization:`Bearer ${this.settings.accessToken}`,"Content-Type":"application/json"},body:JSON.stringify(e)}),o=t.json||JSON.parse(t.text);r.log("\u{1F4CB} Replace placeholder response:",o),o.code!==0?(r.warn(`\u26A0\uFE0F Failed to replace placeholder text: ${o.msg}, trying delete method...`),await this.deletePlaceholderBlock(i,A)):r.log(`\u2705 Replaced placeholder text in block: ${A.blockId}`)}catch(e){r.error("Replace placeholder text error:",e);try{await this.deletePlaceholderBlock(i,A)}catch(t){r.error("Both replace and delete failed:",t)}}}async deletePlaceholderBlock(i,A){try{let e={start_index:A.index,end_index:A.index+1},t=await(0,V.requestUrl)({url:`${q.BASE_URL}/docx/v1/documents/${i}/blocks/${A.parentId}/children/batch_delete`,method:"DELETE",headers:{Authorization:`Bearer ${this.settings.accessToken}`,"Content-Type":"application/json"},body:JSON.stringify(e)}),o=t.json||JSON.parse(t.text);if(o.code!==0)throw new Error(o.msg||"\u5220\u9664\u5360\u4F4D\u7B26\u5757\u5931\u8D25");r.log(`\u2705 Deleted placeholder block: ${A.blockId}`)}catch(e){throw r.error("Delete placeholder block error:",e),e}}async readLocalFile(i){var A;try{let e=i.trim();e=e.replace(/^\.[\\/]/,"");let t=(0,V.normalizePath)(e);r.log(`\u{1F50D} Trying to read file: "${i}" -> "${t}"`);let o=this.app.vault.getFileByPath(t);if(!o){let E=this.app.vault.getFiles(),I=(A=t.split("/").pop())==null?void 0:A.toLowerCase();if(I){let g=E.find(a=>a.name.toLowerCase()===I);g&&(o=g,r.log(`\u2705 Found file by name: ${o.path}`))}}if(!o){r.warn(`\u274C File not found: ${t}`);let I=this.app.vault.getFiles().filter(g=>g.name.includes(t.split("/").pop()||""));return I.length>0&&r.log("\u{1F4CB} Similar files found:",I.map(g=>g.path)),null}let s=await this.app.vault.readBinary(o);return r.log(`\u2705 Successfully read file: ${o.path} (${s.byteLength} bytes)`),s}catch(e){return r.error(`\u274C Error reading local file ${i}:`,e),null}}async processFileUploads(i,A,e){if(A.length===0){r.log("\u{1F4DD} No local files to process");return}try{e&&e.setMessage(`\u{1F50D} \u6B63\u5728\u67E5\u627E\u5360\u4F4D\u7B26 (${A.length} \u4E2A\u6587\u4EF6)...`);let t=await this.findPlaceholderBlocks(i,A);if(t.length===0){r.warn("\u26A0\uFE0F No placeholder blocks found in document");return}r.log(`\u{1F3AF} Found ${t.length} placeholder blocks to process`);let o=this.sortPlaceholdersByOriginalOrder(t,A);r.log("\u{1F4CB} Sorted placeholder blocks by original order"),e&&e.setMessage(`\u{1F4D6} \u6B63\u5728\u5E76\u884C\u8BFB\u53D6 ${o.length} \u4E2A\u6587\u4EF6...`);let s=o.map(async a=>{try{let n=await this.readLocalFile(a.fileInfo.originalPath);return{placeholderBlock:a,fileContent:n,success:!!n}}catch(n){return r.warn(`\u26A0\uFE0F Failed to read file: ${a.fileInfo.originalPath}`,n),{placeholderBlock:a,fileContent:null,success:!1}}}),I=(await Promise.all(s)).filter(a=>a.success);r.log(`\uFFFD Successfully read ${I.length}/${o.length} files`);let g=[];for(let a=0;a<I.length;a++){let{placeholderBlock:n,fileContent:l}=I[a],c=n.fileInfo;e&&e.setMessage(`\u{1F4E4} \u6B63\u5728\u4E0A\u4F20\u6587\u4EF6 ${a+1}/${I.length}: ${c.fileName}...`);try{let Q={...n,index:n.index+a};r.log(`\u{1F4CD} Adjusted insert position for ${c.fileName}: ${n.index} -> ${Q.index}`);let Y=await this.insertFileBlock(i,Q),h=await this.uploadFileToDocument(i,Y,c,l);await this.setFileBlockContent(i,Y,h,c.isImage),g.push(n),r.log(`\u2705 Successfully processed file: ${c.fileName}`)}catch(Q){r.error(`\u274C Failed to process file ${c.fileName}:`,Q)}}if(g.length>0){e&&e.setMessage(`\u{1F504} \u6B63\u5728\u68C0\u67E5\u5E76\u6E05\u7406 ${g.length} \u4E2A\u5360\u4F4D\u7B26...`);let a=await this.findRemainingPlaceholders(i,g);a.length>0?(r.log(`\u{1F504} Found ${a.length} remaining placeholders to clean up`),await this.batchReplacePlaceholderText(i,a)):r.log("\u2705 All placeholders have already been cleaned up")}r.log(`\u{1F389} File upload processing completed: ${g.length} files processed`)}catch(t){throw r.error("Process file uploads error:",t),t}}sortPlaceholdersByOriginalOrder(i,A){r.log("\u{1F4CB} Original localFiles order:"),A.forEach((o,s)=>{r.log(`  ${s}: ${o.fileName} -> ${o.placeholder}`)}),r.log("\u{1F4CB} Found placeholder blocks:"),i.forEach((o,s)=>{r.log(`  ${s}: ${o.fileInfo.fileName} -> ${o.placeholder} (index: ${o.index})`)});let e=new Map;A.forEach((o,s)=>{e.set(o.placeholder,s)});let t=i.sort((o,s)=>{var g,a;let E=(g=e.get(o.placeholder))!=null?g:999,I=(a=e.get(s.placeholder))!=null?a:999;return r.log(`\u{1F504} Comparing: ${o.fileInfo.fileName}(order:${E}, index:${o.index}) vs ${s.fileInfo.fileName}(order:${I}, index:${s.index})`),E!==I?E-I:o.index-s.index});return r.log("\u{1F4CB} Sorted placeholder blocks:"),t.forEach((o,s)=>{r.log(`  ${s}: ${o.fileInfo.fileName} -> ${o.placeholder}`)}),t}async processSubDocuments(i,A,e){r.log(`\u{1F680} Starting sub-document processing for ${A.length} documents`);for(let t=0;t<A.length;t++){let o=A[t];try{e&&e.setMessage(`\u{1F4C4} \u6B63\u5728\u5904\u7406\u5B50\u6587\u6863 ${t+1}/${A.length}: ${o.fileName}...`),r.log(`\u{1F4C4} Processing sub-document: ${o.fileName} (${o.originalPath})`);let s=await this.readSubDocumentContent(o.originalPath);if(!s){r.warn(`\u26A0\uFE0F Could not read sub-document: ${o.originalPath}, skipping...`);continue}let E=await this.uploadSubDocument(o.fileName,s,e);if(!E.success){r.warn(`\u26A0\uFE0F Failed to upload sub-document: ${o.fileName}, error: ${E.error}`);continue}await this.insertSubDocumentLink(i,o,E),r.log(`\u2705 Successfully processed sub-document: ${o.fileName}`)}catch(s){r.error(`\u274C Error processing sub-document ${o.fileName}:`,s)}}r.log("\u2705 Completed sub-document processing")}async readSubDocumentContent(i){var A;try{let e=i.trim(),t=(0,V.normalizePath)(e);r.log(`\u{1F50D} Reading sub-document: "${i}" -> "${t}"`);let o=this.app.vault.getFileByPath(t);if(!o){let E=this.app.vault.getMarkdownFiles(),I=(A=t.split("/").pop())==null?void 0:A.toLowerCase();if(I){let g=E.find(a=>a.name.toLowerCase()===I);g&&(o=g,r.log(`\u2705 Found sub-document by name: ${o.path}`))}}if(!o)return r.warn(`\u274C Sub-document not found: ${t}`),null;let s=await this.app.vault.read(o);return r.log(`\u2705 Successfully read sub-document: ${o.path} (${s.length} characters)`),s}catch(e){return r.error(`\u274C Error reading sub-document ${i}:`,e),null}}async uploadSubDocument(i,A,e){try{r.log(`\u{1F4E4} Uploading sub-document: ${i}`);let t=await this.uploadMarkdownFile(i,A);if(!t.success)return{success:!1,error:t.error||"\u5B50\u6587\u6863\u4E0A\u4F20\u5931\u8D25"};let o=i.endsWith(".md")?i.slice(0,-3):i,s=await this.createImportTaskWithCorrectFolder(t.fileToken,o);if(!s.success)return{success:!1,error:s.error||"\u5B50\u6587\u6863\u5BFC\u5165\u4EFB\u52A1\u521B\u5EFA\u5931\u8D25"};let E=await this.waitForImportCompletionWithTimeout(s.ticket,15e3);if(E.success&&E.documentToken){let I=`https://feishu.cn/docx/${E.documentToken}`,g=[];if(this.settings.enableLinkShare){let n=(async()=>{try{e&&e.setMessage(`\u{1F517} \u6B63\u5728\u8BBE\u7F6E\u5B50\u6587\u6863\u6743\u9650: ${o}...`),r.log(`\u{1F517} Setting permissions for sub-document: ${o}`),await this.setDocumentSharePermissions(E.documentToken,!0),r.log(`\u2705 Sub-document permissions set successfully: ${o}`)}catch(l){r.warn(`\u26A0\uFE0F Failed to set sub-document permissions for ${o}:`,l)}})();g.push(n)}let a=(async()=>{try{await this.deleteSourceFile(t.fileToken)}catch(n){r.warn("\u26A0\uFE0F Failed to delete sub-document source file:",n)}})();return g.push(a),await Promise.allSettled(g),{success:!0,documentToken:E.documentToken,url:I,title:o}}else return{success:!1,error:"\u5B50\u6587\u6863\u5BFC\u5165\u8D85\u65F6\u6216\u5931\u8D25"}}catch(t){return r.error("Upload sub-document error:",t),{success:!1,error:t.message}}}async insertSubDocumentLink(i,A,e){try{r.log(`\u{1F517} Inserting sub-document link for: ${A.fileName}`);let t=await this.findPlaceholderBlocks(i,[A]);if(t.length===0){r.warn(`\u26A0\uFE0F No placeholder found for sub-document: ${A.fileName}`);return}let o=t[0],s=`\u{1F4C4} [${e.title}](${e.url})`;await this.replaceTextInBlock(i,o.blockId,s),r.log(`\u2705 Successfully inserted sub-document link: ${A.fileName}`)}catch(t){r.error(`\u274C Error inserting sub-document link for ${A.fileName}:`,t)}}async replaceTextInBlock(i,A,e){try{let t={update_text_elements:{elements:[{text_run:{content:e}}]}};r.log(`\u{1F527} Replacing text in block ${A} with: "${e}"`);let o=await(0,V.requestUrl)({url:`${q.BASE_URL}/docx/v1/documents/${i}/blocks/${A}`,method:"PATCH",headers:{Authorization:`Bearer ${this.settings.accessToken}`,"Content-Type":"application/json"},body:JSON.stringify(t)}),s=o.json||JSON.parse(o.text);if(r.log("\u{1F4CB} Replace text response:",s),s.code!==0)throw new Error(s.msg||"\u66FF\u6362\u6587\u672C\u5931\u8D25");r.log(`\u2705 Successfully replaced text in block: ${A}`)}catch(t){throw r.error(`\u274C Error replacing text in block ${A}:`,t),t}}async setDocumentSharePermissions(i,A=!1){try{if(!await this.ensureValidToken())throw new Error("Token\u65E0\u6548\uFF0C\u8BF7\u91CD\u65B0\u6388\u6743");if(A)r.log(`\u{1F527} Setting document permissions (skipping check): ${this.settings.linkSharePermission}`);else try{let I=(await this.getDocumentPermissions(i)).link_share_entity,g=this.settings.linkSharePermission;if(I===g){r.log(`\u2705 Document permissions already correct: ${I}`);return}r.log(`\u{1F504} Document permissions need update: ${I} \u2192 ${g}`)}catch(E){r.warn("\u26A0\uFE0F Failed to get current permissions, proceeding with update:",E)}let t={};this.settings.enableLinkShare&&(t.link_share_entity=this.settings.linkSharePermission,this.settings.linkSharePermission==="anyone_readable"||this.settings.linkSharePermission,t.external_access_entity="open",t.share_entity="anyone",t.manage_collaborator_entity="collaborator_can_view"),r.log(`\u{1F527} Setting document share permissions for ${i}:`,t);let o=await(0,V.requestUrl)({url:`${q.BASE_URL}/drive/v2/permissions/${i}/public?type=docx`,method:"PATCH",headers:{Authorization:`Bearer ${this.settings.accessToken}`,"Content-Type":"application/json"},body:JSON.stringify(t)});r.log(`\u{1F4CB} Set document permissions response status: ${o.status}`);let s;try{s=o.json||JSON.parse(o.text)}catch(E){throw r.error("\u274C Failed to parse response:",o.text),new Error(`API\u54CD\u5E94\u89E3\u6790\u5931\u8D25: ${o.status} - ${o.text}`)}if(r.log("\u{1F4CB} Set document permissions response data:",s),s.code!==0)throw r.error("\u274C API returned error:",{code:s.code,msg:s.msg,requestData:t,documentToken:i}),new Error(`\u8BBE\u7F6E\u6587\u6863\u5206\u4EAB\u6743\u9650\u5931\u8D25 (${s.code}): ${s.msg}`);r.log(`\u2705 Successfully set document share permissions for ${i}`)}catch(e){throw r.error("Set document share permissions error:",e),e}}async getDocumentPermissions(i){try{if(!await this.ensureValidToken())throw new Error("Token\u65E0\u6548\uFF0C\u8BF7\u91CD\u65B0\u6388\u6743");let e=await(0,V.requestUrl)({url:`${q.BASE_URL}/drive/v2/permissions/${i}/public?type=docx`,method:"GET",headers:{Authorization:`Bearer ${this.settings.accessToken}`,"Content-Type":"application/json"}}),t=e.json||JSON.parse(e.text);if(t.code!==0)throw new Error(t.msg||"\u83B7\u53D6\u6587\u6863\u6743\u9650\u8BBE\u7F6E\u5931\u8D25");return t.data.permission_public}catch(A){throw r.error("Get document permissions error:",A),A}}async verifyDocumentLinkSharing(i){try{let A=await this.getDocumentPermissions(i);r.log("\u{1F50D} Analyzing document permissions:",A);let e=A.link_share_entity,t=A.external_access_entity,o=!1,s="none",E="none",I="";e==="close"?I="\u94FE\u63A5\u5206\u4EAB\u5DF2\u5173\u95ED\uFF0C\u53EA\u6709\u534F\u4F5C\u8005\u53EF\u4EE5\u8BBF\u95EE\u6587\u6863":e==="tenant_readable"?(o=!0,s="tenant",E="readable",I="\u7EC4\u7EC7\u5185\u83B7\u5F97\u94FE\u63A5\u7684\u4EBA\u53EF\u4EE5\u9605\u8BFB\u6587\u6863"):e==="tenant_editable"?(o=!0,s="tenant",E="editable",I="\u7EC4\u7EC7\u5185\u83B7\u5F97\u94FE\u63A5\u7684\u4EBA\u53EF\u4EE5\u7F16\u8F91\u6587\u6863"):e==="anyone_can_view"&&t==="open"?(o=!0,s="internet",E="readable",I="\u4E92\u8054\u7F51\u4E0A\u83B7\u5F97\u94FE\u63A5\u7684\u4EFB\u4F55\u4EBA\u90FD\u53EF\u4EE5\u9605\u8BFB\u6587\u6863"):e==="anyone_can_edit"&&t==="open"?(o=!0,s="internet",E="editable",I="\u4E92\u8054\u7F51\u4E0A\u83B7\u5F97\u94FE\u63A5\u7684\u4EFB\u4F55\u4EBA\u90FD\u53EF\u4EE5\u7F16\u8F91\u6587\u6863"):I=`\u672A\u77E5\u7684\u94FE\u63A5\u5206\u4EAB\u8BBE\u7F6E: ${e}, external_access: ${t}`;let g={isLinkSharingEnabled:o,shareScope:s,accessLevel:E,explanation:I};return r.log("\u{1F4CA} Link sharing analysis result:",g),g}catch(A){throw r.error("Verify document link sharing error:",A),A}}};var R=require("obsidian");var U=require("obsidian"),j=class extends U.Modal{constructor(A,e,t){super(A);this.feishuApi=e,this.onSuccess=t}onOpen(){let{contentEl:A}=this;A.empty(),A.createEl("h2",{text:"\u{1F510} \u98DE\u4E66\u624B\u52A8\u6388\u6743"});let e=A.createDiv("setting-item-description");e.style.marginBottom="20px";let o=e.createEl("p").createEl("strong");o.textContent="\u{1F680} \u7B80\u5316\u6388\u6743\u6D41\u7A0B - \u53EA\u9700\u590D\u5236\u7C98\u8D34URL\uFF1A";let s=e.createEl("ol");s.createEl("li").textContent='\u70B9\u51FB\u4E0B\u65B9\u7684"\u6253\u5F00\u6388\u6743\u9875\u9762"\u6309\u94AE',s.createEl("li").textContent="\u5728\u5F39\u51FA\u7684\u98DE\u4E66\u9875\u9762\u4E2D\u767B\u5F55\u5E76\u786E\u8BA4\u6388\u6743",s.createEl("li").textContent="\u6388\u6743\u6210\u529F\u540E\uFF0C\u4F1A\u8DF3\u8F6C\u5230\u4E00\u4E2A\u663E\u793A\u9519\u8BEF\u7684\u9875\u9762\uFF08\u8FD9\u662F\u6B63\u5E38\u7684\uFF09";let E=s.createEl("li");E.createEl("strong").textContent="\u590D\u5236\u6D4F\u89C8\u5668\u5730\u5740\u680F\u7684\u5B8C\u6574URL",E.appendText("\uFF08\u5305\u542B code= \u53C2\u6570\uFF09"),s.createEl("li").textContent="\u5C06\u5B8C\u6574URL\u7C98\u8D34\u5230\u4E0B\u65B9\u8F93\u5165\u6846\u4E2D",s.createEl("li").textContent='\u70B9\u51FB"\u5B8C\u6210\u6388\u6743"\u6309\u94AE';let I=e.createDiv();I.style.cssText=`
			background: var(--background-modifier-success);
			padding: 10px;
			border-radius: 4px;
			margin-top: 10px;
		`;let g=I.createEl("strong");g.textContent="\u{1F4A1} \u63D0\u793A\uFF1A",I.appendText("\u65E0\u9700\u624B\u52A8\u63D0\u53D6\u6388\u6743\u7801\uFF0C\u76F4\u63A5\u590D\u5236\u5B8C\u6574\u7684\u56DE\u8C03URL\u5373\u53EF\uFF01"),new U.Setting(A).setName("\u7B2C\u4E00\u6B65\uFF1A\u6253\u5F00\u6388\u6743\u9875\u9762").setDesc("\u70B9\u51FB\u6309\u94AE\u5728\u6D4F\u89C8\u5668\u4E2D\u6253\u5F00\u98DE\u4E66\u6388\u6743\u9875\u9762").addButton(n=>{n.setButtonText("\u{1F310} \u6253\u5F00\u6388\u6743\u9875\u9762").setCta().onClick(()=>{try{let l=this.feishuApi.generateAuthUrl();window.open(l,"_blank"),new U.Notice("\u2705 \u6388\u6743\u9875\u9762\u5DF2\u6253\u5F00\uFF0C\u8BF7\u5728\u6D4F\u89C8\u5668\u4E2D\u5B8C\u6210\u6388\u6743")}catch(l){new U.Notice(`\u274C \u751F\u6210\u6388\u6743\u94FE\u63A5\u5931\u8D25: ${l.message}`)}})});let a="";new U.Setting(A).setName("\u7B2C\u4E8C\u6B65\uFF1A\u7C98\u8D34\u56DE\u8C03URL").setDesc("\u4ECE\u6D4F\u89C8\u5668\u5730\u5740\u680F\u590D\u5236\u5B8C\u6574\u7684\u56DE\u8C03URL\u5E76\u7C98\u8D34\u5230\u6B64\u5904").addTextArea(n=>{n.setPlaceholder("\u7C98\u8D34\u5B8C\u6574\u7684\u56DE\u8C03URL\uFF0C\u4F8B\u5982\uFF1Ahttps://example.com/callback?code=xxx&state=xxx").setValue(a).onChange(l=>{a=l.trim()}),n.inputEl.style.width="100%",n.inputEl.style.height="80px"}),new U.Setting(A).setName("\u7B2C\u4E09\u6B65\uFF1A\u5B8C\u6210\u6388\u6743").setDesc("\u89E3\u6790\u56DE\u8C03URL\u5E76\u5B8C\u6210\u6388\u6743\u6D41\u7A0B").addButton(n=>{n.setButtonText("\u2705 \u5B8C\u6210\u6388\u6743").setCta().onClick(async()=>{await this.processCallback(a)})}),new U.Setting(A).addButton(n=>{n.setButtonText("\u53D6\u6D88").onClick(()=>{this.close()})})}async processCallback(A){try{if(!A){new U.Notice("\u274C \u8BF7\u5148\u7C98\u8D34\u56DE\u8C03URL");return}let e=new URL(A),t=e.searchParams.get("code"),o=e.searchParams.get("state");if(!t){new U.Notice("\u274C \u56DE\u8C03URL\u4E2D\u672A\u627E\u5230\u6388\u6743\u7801\uFF0C\u8BF7\u68C0\u67E5URL\u662F\u5426\u5B8C\u6574");return}let s=localStorage.getItem("feishu-oauth-state");if(s&&o!==s){new U.Notice("\u274C \u72B6\u6001\u9A8C\u8BC1\u5931\u8D25\uFF0C\u8BF7\u91CD\u65B0\u6388\u6743");return}new U.Notice("\u{1F504} \u6B63\u5728\u5904\u7406\u6388\u6743..."),await this.feishuApi.handleOAuthCallback(t)?(new U.Notice("\u{1F389} \u6388\u6743\u6210\u529F\uFF01"),this.onSuccess(),this.close()):new U.Notice("\u274C \u6388\u6743\u5904\u7406\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5")}catch(e){console.error("Process callback error:",e),new U.Notice(`\u274C \u5904\u7406\u6388\u6743\u65F6\u53D1\u751F\u9519\u8BEF: ${e.message}`)}}onClose(){let{contentEl:A}=this;A.empty()}};var y=require("obsidian"),S=class extends y.Modal{constructor(A,e,t){super(A);this.folders=[];this.currentPath=[];this.loading=!1;this.feishuApi=e,this.onSelect=t}onOpen(){let{contentEl:A}=this;A.empty(),A.createEl("h2",{text:"\u9009\u62E9\u6587\u4EF6\u5939"}),this.createBreadcrumb(A);let e=A.createDiv("folder-list-container");e.style.cssText=`
			max-height: 400px;
			overflow-y: auto;
			border: 1px solid var(--background-modifier-border);
			border-radius: 8px;
			margin: 16px 0;
		`;let t=A.createDiv("button-container");t.style.cssText=`
			display: flex;
			justify-content: space-between;
			margin-top: 16px;
		`;let o=t.createEl("button",{text:"\u9009\u62E9\u5F53\u524D\u6587\u4EF6\u5939",cls:"mod-cta"});o.onclick=()=>{let E=this.currentPath.length>0?this.currentPath[this.currentPath.length-1]:null;this.onSelect(E),this.close()};let s=t.createEl("button",{text:"\u53D6\u6D88"});s.onclick=()=>{this.close()},this.loadFolders(e)}createBreadcrumb(A){let e=A.createDiv("folder-breadcrumb");e.style.cssText=`
			display: flex;
			align-items: center;
			gap: 8px;
			margin: 16px 0;
			padding: 8px 12px;
			background: var(--background-secondary);
			border-radius: 6px;
			font-size: 14px;
		`;let t=e.createSpan("breadcrumb-item");t.textContent="\u6211\u7684\u7A7A\u95F4",t.style.cssText=`
			cursor: pointer;
			color: var(--text-accent);
			text-decoration: underline;
		`,t.onclick=()=>this.navigateToRoot(),this.currentPath.forEach((o,s)=>{e.createSpan("breadcrumb-separator").textContent=" / ";let E=e.createSpan("breadcrumb-item");E.textContent=o.name,s<this.currentPath.length-1?(E.style.cssText=`
					cursor: pointer;
					color: var(--text-accent);
					text-decoration: underline;
				`,E.onclick=()=>this.navigateToFolder(s)):E.style.cssText=`
					font-weight: bold;
					color: var(--text-normal);
				`})}async loadFolders(A){if(this.loading)return;this.loading=!0,A.empty();let e=A.createDiv("loading-indicator");e.textContent="\u6B63\u5728\u52A0\u8F7D\u6587\u4EF6\u5939...",e.style.cssText=`
			text-align: center;
			padding: 20px;
			color: var(--text-muted);
		`;try{let t=this.currentPath.length>0?this.currentPath[this.currentPath.length-1].folder_token||this.currentPath[this.currentPath.length-1].token:void 0,o=await this.feishuApi.getFolderList(t);this.folders=o.data.folders,A.empty(),this.renderFolderList(A)}catch(t){console.error("Failed to load folders:",t),A.empty();let o=A.createDiv("error-message");o.textContent=`\u52A0\u8F7D\u5931\u8D25: ${t.message}`,o.style.cssText=`
				text-align: center;
				padding: 20px;
				color: var(--text-error);
			`}finally{this.loading=!1}}renderFolderList(A){if(this.folders.length===0){let e=A.createDiv("empty-message");e.textContent="\u6B64\u6587\u4EF6\u5939\u4E3A\u7A7A",e.style.cssText=`
				text-align: center;
				padding: 20px;
				color: var(--text-muted);
			`;return}this.folders.forEach(e=>{let t=A.createDiv("folder-item");t.style.cssText=`
				display: flex;
				align-items: center;
				padding: 12px 16px;
				cursor: pointer;
				border-bottom: 1px solid var(--background-modifier-border);
				transition: background-color 0.2s;
			`;let o=t.createSpan("folder-icon");o.textContent="\u{1F4C1}",o.style.cssText=`
				margin-right: 12px;
				font-size: 16px;
			`;let s=t.createSpan("folder-name");s.textContent=e.name,s.style.cssText=`
				flex: 1;
				font-size: 14px;
			`,t.onmouseenter=()=>{t.style.backgroundColor="var(--background-modifier-hover)"},t.onmouseleave=()=>{t.style.backgroundColor=""},t.onclick=()=>{this.enterFolder(e)}})}async enterFolder(A){let e=this.currentPath.findIndex(s=>(s.folder_token||s.token)===(A.folder_token||A.token));e>=0?this.currentPath=this.currentPath.slice(0,e+1):this.currentPath.push(A);let t=this.contentEl.querySelector(".folder-breadcrumb");t&&(t.remove(),this.createBreadcrumb(this.contentEl));let o=this.contentEl.querySelector(".folder-list-container");o&&await this.loadFolders(o)}async navigateToRoot(){this.currentPath=[];let A=this.contentEl.querySelector(".folder-breadcrumb");A&&(A.remove(),this.createBreadcrumb(this.contentEl));let e=this.contentEl.querySelector(".folder-list-container");e&&await this.loadFolders(e)}async navigateToFolder(A){this.currentPath=this.currentPath.slice(0,A+1);let e=this.contentEl.querySelector(".folder-breadcrumb");e&&(e.remove(),this.createBreadcrumb(this.contentEl));let t=this.contentEl.querySelector(".folder-list-container");t&&await this.loadFolders(t)}onClose(){let{contentEl:A}=this;A.empty()}};var D=class extends R.PluginSettingTab{constructor(A,e){super(A,e);this.plugin=e}display(){let{containerEl:A}=this;A.empty(),A.createEl("h2",{text:"\u98DE\u4E66\u5206\u4EAB\u8BBE\u7F6E"});let e=A.createDiv("setting-item-description"),t=e.createEl("p");t.textContent="\u76F4\u8FDE\u98DE\u4E66API\uFF0C\u56DE\u8C03\u5730\u5740\u4EC5\u4E2D\u8F6C\u65E0\u8BB0\u5F55\u3002";let o=e.createEl("p");o.createEl("strong").textContent="\u7279\u70B9\uFF1A",o.appendText("\u65E0\u4F9D\u8D56\u3001\u66F4\u5B89\u5168\u3001\u54CD\u5E94\u66F4\u5FEB"),A.createEl("h3",{text:"\u{1F527} \u5E94\u7528\u914D\u7F6E"}),new R.Setting(A).setName("App ID").setDesc("\u98DE\u4E66\u5E94\u7528\u7684 App ID").addText(C=>C.setPlaceholder("\u8F93\u5165\u98DE\u4E66\u5E94\u7528\u7684 App ID").setValue(this.plugin.settings.appId).onChange(async B=>{this.plugin.settings.appId=B.trim(),await this.plugin.saveSettings()})),new R.Setting(A).setName("App Secret").setDesc("\u98DE\u4E66\u5E94\u7528\u7684 App Secret").addText(C=>{C.setPlaceholder("\u8F93\u5165\u98DE\u4E66\u5E94\u7528\u7684 App Secret").setValue(this.plugin.settings.appSecret).onChange(async B=>{this.plugin.settings.appSecret=B.trim(),await this.plugin.saveSettings()}),C.inputEl.type="password"}),new R.Setting(A).setName("OAuth\u56DE\u8C03\u5730\u5740").setDesc("obsidian\u9700web\u56DE\u8C03\u4E2D\u8F6C\uFF0C\u4F8B\u5982\uFF1Ahttps://md2feishu.xinqi.life/oauth-callback").addText(C=>C.setPlaceholder("https://md2feishu.xinqi.life/oauth-callback").setValue(this.plugin.settings.callbackUrl).onChange(async B=>{this.plugin.settings.callbackUrl=B.trim(),await this.plugin.saveSettings()})),A.createEl("h3",{text:"\u{1F510} \u6388\u6743\u7BA1\u7406"});let E=A.createDiv("setting-item").createDiv("setting-item-info");E.createDiv("setting-item-name").setText("\u6388\u6743\u72B6\u6001");let I=E.createDiv("setting-item-description");if(this.plugin.settings.userInfo){let C=I.createEl("span",{text:"\u2705 \u5DF2\u6388\u6743"});C.style.color="var(--text-success)",I.createEl("br");let B=I.createDiv();B.style.marginTop="4px";let L=B.createEl("strong");L.textContent="\u7528\u6237\uFF1A",B.appendText(this.plugin.settings.userInfo.name),B.createEl("br");let v=B.createEl("strong");v.textContent="\u90AE\u7BB1\uFF1A",B.appendText(this.plugin.settings.userInfo.email)}else{let C=I.createEl("span",{text:"\u274C \u672A\u6388\u6743"});C.style.color="var(--text-error)"}new R.Setting(A).setName("\u{1F680} \u4E00\u952E\u6388\u6743\uFF08\u63A8\u8350\uFF09").setDesc("\u81EA\u52A8\u6253\u5F00\u6D4F\u89C8\u5668\u5B8C\u6210\u6388\u6743\uFF0C\u901A\u8FC7\u4E91\u7AEF\u56DE\u8C03\u81EA\u52A8\u8FD4\u56DE\u6388\u6743\u7ED3\u679C\uFF0C\u65E0\u9700\u624B\u52A8\u64CD\u4F5C").addButton(C=>{C.setButtonText("\u{1F680} \u4E00\u952E\u6388\u6743").setCta().onClick(()=>{this.startAutoAuth()})}),new R.Setting(A).setName("\u{1F4DD} \u624B\u52A8\u6388\u6743\uFF08\u5907\u7528\uFF09").setDesc("\u5982\u679C\u4E00\u952E\u6388\u6743\u9047\u5230\u95EE\u9898\uFF0C\u53EF\u4EE5\u4F7F\u7528\u4F20\u7EDF\u7684\u624B\u52A8\u590D\u5236\u7C98\u8D34\u6388\u6743\u65B9\u5F0F").addButton(C=>{C.setButtonText("\u624B\u52A8\u6388\u6743").onClick(()=>{this.startManualAuth()})}),this.plugin.settings.userInfo&&new R.Setting(A).setName("\u6E05\u9664\u6388\u6743").setDesc("\u6E05\u9664\u5F53\u524D\u7684\u6388\u6743\u4FE1\u606F").addButton(C=>{C.setButtonText("\u{1F5D1}\uFE0F \u6E05\u9664\u6388\u6743").setWarning().onClick(async()=>{this.plugin.settings.accessToken="",this.plugin.settings.refreshToken="",this.plugin.settings.userInfo=null,await this.plugin.saveSettings(),this.plugin.feishuApi.updateSettings(this.plugin.settings),new R.Notice("\u2705 \u6388\u6743\u4FE1\u606F\u5DF2\u6E05\u9664"),this.display()})}),A.createEl("h3",{text:"\u{1F4DD} \u5185\u5BB9\u5904\u7406\u8BBE\u7F6E"}),new R.Setting(A).setName("\u6587\u6863\u6807\u9898\u6765\u6E90").setDesc("\u9009\u62E9\u751F\u6210\u7684\u98DE\u4E66\u6587\u6863\u6807\u9898\u4F7F\u7528\u54EA\u4E2A\u6765\u6E90").addDropdown(C=>{C.addOption("filename","\u6587\u4EF6\u540D (Filename)").addOption("frontmatter",'YAML Front Matter \u7684 "title" \u5C5E\u6027').setValue(this.plugin.settings.titleSource).onChange(async B=>{this.plugin.settings.titleSource=B,await this.plugin.saveSettings()})}),new R.Setting(A).setName("Front Matter \u5904\u7406").setDesc("\u9009\u62E9\u5982\u4F55\u5904\u7406\u7B14\u8BB0\u9876\u90E8\u7684 YAML \u5C5E\u6027\u533A").addDropdown(C=>{C.addOption("remove","\u79FB\u9664 (Remove)").addOption("keep-as-code","\u4FDD\u7559\u4E3A\u4EE3\u7801\u5757 (Keep as Code Block)").setValue(this.plugin.settings.frontMatterHandling).onChange(async B=>{this.plugin.settings.frontMatterHandling=B,await this.plugin.saveSettings()})}),new R.Setting(A).setName("\u5B50\u6587\u6863\u4E0A\u4F20").setDesc("\u662F\u5426\u5904\u7406\u548C\u4E0A\u4F20\u7B14\u8BB0\u4E2D\u5F15\u7528\u7684\u5176\u4ED6 Markdown \u6587\u4EF6\u4F5C\u4E3A\u5B50\u6587\u6863").addToggle(C=>{C.setValue(this.plugin.settings.enableSubDocumentUpload).onChange(async B=>{this.plugin.settings.enableSubDocumentUpload=B,await this.plugin.saveSettings()})}),new R.Setting(A).setName("\u672C\u5730\u56FE\u7247\u4E0A\u4F20").setDesc("\u662F\u5426\u4E0A\u4F20\u7B14\u8BB0\u4E2D\u5F15\u7528\u7684\u672C\u5730\u56FE\u7247\u6587\u4EF6\u5230\u98DE\u4E66").addToggle(C=>{C.setValue(this.plugin.settings.enableLocalImageUpload).onChange(async B=>{this.plugin.settings.enableLocalImageUpload=B,await this.plugin.saveSettings()})}),new R.Setting(A).setName("\u672C\u5730\u9644\u4EF6\u4E0A\u4F20").setDesc("\u662F\u5426\u4E0A\u4F20\u7B14\u8BB0\u4E2D\u5F15\u7528\u7684\u672C\u5730\u9644\u4EF6\u6587\u4EF6\uFF08\u5982 PDF\u3001Word \u7B49\uFF09\u5230\u98DE\u4E66").addToggle(C=>{C.setValue(this.plugin.settings.enableLocalAttachmentUpload).onChange(async B=>{this.plugin.settings.enableLocalAttachmentUpload=B,await this.plugin.saveSettings()})}),new R.Setting(A).setName("\u81EA\u52A8\u6DFB\u52A0\u5206\u4EAB\u6807\u8BB0").setDesc("\u5206\u4EAB\u6210\u529F\u540E\uFF0C\u81EA\u52A8\u5728\u7B14\u8BB0\u7684 Front Matter \u4E2D\u6DFB\u52A0\u5206\u4EAB\u6807\u8BB0\uFF08feishushare: true\u3001\u5206\u4EAB\u94FE\u63A5\u548C\u65F6\u95F4\uFF09").addToggle(C=>{C.setValue(this.plugin.settings.enableShareMarkInFrontMatter).onChange(async B=>{this.plugin.settings.enableShareMarkInFrontMatter=B,await this.plugin.saveSettings()})}),A.createEl("h3",{text:"\u{1F517} \u5206\u4EAB\u6743\u9650\u8BBE\u7F6E"}),new R.Setting(A).setName("\u542F\u7528\u94FE\u63A5\u5206\u4EAB").setDesc("\u662F\u5426\u4E3A\u5206\u4EAB\u7684\u6587\u6863\u8BBE\u7F6E\u94FE\u63A5\u5206\u4EAB\u6743\u9650\uFF0C\u8BA9\u7EC4\u7EC7\u5185\u7684\u4EBA\u53EF\u4EE5\u901A\u8FC7\u94FE\u63A5\u8BBF\u95EE").addToggle(C=>{C.setValue(this.plugin.settings.enableLinkShare).onChange(async B=>{this.plugin.settings.enableLinkShare=B,await this.plugin.saveSettings(),this.display()})}),this.plugin.settings.enableLinkShare&&new R.Setting(A).setName("\u94FE\u63A5\u5206\u4EAB\u6743\u9650").setDesc("\u8BBE\u7F6E\u83B7\u5F97\u94FE\u63A5\u7684\u4EBA\u7684\u8BBF\u95EE\u6743\u9650\u3002\u6CE8\u610F\uFF1A\u4E92\u8054\u7F51\u8BBF\u95EE\u9700\u8981\u4F01\u4E1A\u7BA1\u7406\u5458\u5141\u8BB8\u5916\u90E8\u5206\u4EAB").addDropdown(C=>{C.addOption("anyone_readable","\u{1F310} \u4E92\u8054\u7F51\u4E0A\u83B7\u5F97\u94FE\u63A5\u7684\u4EFB\u4F55\u4EBA\u53EF\u9605\u8BFB").addOption("anyone_editable","\u{1F310} \u4E92\u8054\u7F51\u4E0A\u83B7\u5F97\u94FE\u63A5\u7684\u4EFB\u4F55\u4EBA\u53EF\u7F16\u8F91").addOption("tenant_readable","\u{1F3E2} \u7EC4\u7EC7\u5185\u83B7\u5F97\u94FE\u63A5\u7684\u4EBA\u53EF\u9605\u8BFB").addOption("tenant_editable","\u{1F3E2} \u7EC4\u7EC7\u5185\u83B7\u5F97\u94FE\u63A5\u7684\u4EBA\u53EF\u7F16\u8F91").setValue(this.plugin.settings.linkSharePermission).onChange(async B=>{this.plugin.settings.linkSharePermission=B,await this.plugin.saveSettings()})}),this.plugin.settings.userInfo&&(A.createEl("h3",{text:"\u{1F4C1} \u9ED8\u8BA4\u6587\u4EF6\u5939"}),new R.Setting(A).setName("\u5F53\u524D\u9ED8\u8BA4\u6587\u4EF6\u5939").setDesc(`\u6587\u6863\u5C06\u4FDD\u5B58\u5230\uFF1A${this.plugin.settings.defaultFolderName||"\u6211\u7684\u7A7A\u95F4"}${this.plugin.settings.defaultFolderId?` (ID: ${this.plugin.settings.defaultFolderId})`:""}`).addButton(C=>{C.setButtonText("\u{1F4C1} \u9009\u62E9\u6587\u4EF6\u5939").onClick(()=>{this.showFolderSelectModal()})})),A.createEl("h3",{text:"\u{1F4D6} \u4F7F\u7528\u8BF4\u660E"});let g=A.createDiv("setting-item-description"),a=g.createDiv();a.createEl("strong",{text:"\u{1F4DA} \u8BE6\u7EC6\u4F7F\u7528\u8BF4\u660E"}),a.createEl("br");let n=a.createEl("a",{text:"\u{1F517} \u70B9\u51FB\u67E5\u770B\u5B8C\u6574\u4F7F\u7528\u6559\u7A0B",href:"https://l0c34idk7v.feishu.cn/docx/Zk2VdWJPfoqmZhxPSJmcMfSbnHe"});n.target="_blank";let l=g.createDiv();l.createEl("strong",{text:"\u{1F4CB} \u5FEB\u901F\u914D\u7F6E\u6307\u5357"});let c=l.createEl("ol"),Q=c.createEl("li");Q.createEl("strong",{text:"\u521B\u5EFA\u98DE\u4E66\u5E94\u7528\uFF1A"}),Q.appendText("\u8BBF\u95EE ");let Y=Q.createEl("a",{text:"\u98DE\u4E66\u5F00\u653E\u5E73\u53F0 \u{1F517}",href:"https://open.feishu.cn/app"});Y.target="_blank",Q.appendText(' \u521B\u5EFA"\u4F01\u4E1A\u81EA\u5EFA\u5E94\u7528"\uFF0C\u83B7\u53D6App ID\u548CApp Secret');let h=c.createEl("li");h.createEl("strong",{text:"\u914D\u7F6EOAuth\u56DE\u8C03\uFF1A"}),h.appendText('\u5728\u98DE\u4E66\u5E94\u7528"\u5B89\u5168\u8BBE\u7F6E"\u4E2D\u6DFB\u52A0\u56DE\u8C03\u5730\u5740\uFF1A'),h.createEl("br"),h.createEl("code",{text:"https://md2feishu.xinqi.life/oauth-callback"}),h.createEl("br"),h.createEl("span",{text:"\u{1F4A1} \u9ED8\u8BA4\u4F7F\u7528\u6211\u4EEC\u7684\u56DE\u8C03\u670D\u52A1\uFF0C\u4EE3\u7801\u5F00\u6E90\u53EF\u81EA\u884C\u90E8\u7F72",cls:"hint"});let x=c.createEl("li");x.createEl("strong",{text:"\u6DFB\u52A0\u5E94\u7528\u6743\u9650\uFF1A"}),x.appendText('\u5728"\u6743\u9650\u7BA1\u7406"\u4E2D\u6DFB\u52A0\u4EE5\u4E0B\u6743\u9650\uFF1A');let G=x.createEl("ul");G.createEl("li",{text:"contact:user.base:readonly - \u83B7\u53D6\u7528\u6237\u57FA\u672C\u4FE1\u606F"}),G.createEl("li",{text:"docx:document - \u521B\u5EFA\u3001\u7F16\u8F91\u6587\u6863"}),G.createEl("li",{text:"drive:drive - \u8BBF\u95EE\u4E91\u7A7A\u95F4\u6587\u4EF6"});let p=c.createEl("li");p.createEl("strong",{text:"\u5B8C\u6210\u6388\u6743\uFF1A"}),p.appendText('\u5728\u4E0A\u65B9\u8F93\u5165App ID\u548CApp Secret\uFF0C\u70B9\u51FB"\u{1F680} \u4E00\u952E\u6388\u6743"');let J=c.createEl("li");J.createEl("strong",{text:"\u9009\u62E9\u6587\u4EF6\u5939\uFF1A"}),J.appendText("\u6388\u6743\u540E\u53EF\u9009\u62E9\u9ED8\u8BA4\u4FDD\u5B58\u6587\u4EF6\u5939\uFF08\u53EF\u9009\uFF09");let z=c.createEl("li");z.createEl("strong",{text:"\u5F00\u59CB\u4F7F\u7528\uFF1A"}),z.appendText('\u53F3\u952EMD\u6587\u4EF6\u9009\u62E9"\u{1F4E4} \u5206\u4EAB\u5230\u98DE\u4E66"\uFF0C\u6216\u4F7F\u7528\u547D\u4EE4\u9762\u677F');let m=g.createDiv();m.createEl("strong",{text:"\u{1F389} \u529F\u80FD\u7279\u8272\uFF1A"});let F=m.createEl("ul");F.createEl("li",{text:"\u2705 \u667A\u80FD\u6388\u6743\uFF1A\u81EA\u52A8\u68C0\u6D4Btoken\u72B6\u6001\uFF0C\u5931\u6548\u65F6\u81EA\u52A8\u91CD\u65B0\u6388\u6743"}),F.createEl("li",{text:"\u2705 \u65E0\u7F1D\u5206\u4EAB\uFF1A\u4E00\u952E\u5206\u4EAB\uFF0C\u81EA\u52A8\u5904\u7406\u6388\u6743\u548C\u8F6C\u6362\u6D41\u7A0B"}),F.createEl("li",{text:"\u2705 \u683C\u5F0F\u4FDD\u6301\uFF1A\u5B8C\u7F8E\u4FDD\u6301Markdown\u683C\u5F0F\uFF0C\u5305\u62EC\u56FE\u7247\u3001\u8868\u683C\u3001\u4EE3\u7801\u5757"}),F.createEl("li",{text:"\u2705 \u667A\u80FD\u5904\u7406\uFF1A\u81EA\u52A8\u5904\u7406Obsidian\u53CC\u5411\u94FE\u63A5\u3001\u6807\u7B7E\u7B49\u8BED\u6CD5"}),F.createEl("li",{text:"\u2705 \u53EF\u89C6\u5316\u9009\u62E9\uFF1A\u652F\u6301\u6D4F\u89C8\u548C\u9009\u62E9\u76EE\u6807\u6587\u4EF6\u5939"}),F.createEl("li",{text:"\u2705 \u94FE\u63A5\u5206\u4EAB\uFF1A\u81EA\u52A8\u8BBE\u7F6E\u6587\u6863\u5206\u4EAB\u6743\u9650\uFF0C\u652F\u6301\u7EC4\u7EC7\u5185\u94FE\u63A5\u8BBF\u95EE"}),F.createEl("li",{text:"\u2705 \u4E00\u952E\u590D\u5236\uFF1A\u5206\u4EAB\u6210\u529F\u540E\u53EF\u4E00\u952E\u590D\u5236\u6587\u6863\u94FE\u63A5"}),this.addAuthorSection(A),this.addRewardSection(A)}addAuthorSection(A){A.createEl("hr");let e=A.createDiv();e.createEl("h4",{text:"\u{1F468}\u200D\u{1F4BB} \u4E86\u89E3\u4F5C\u8005"}),e.createEl("p",{text:"\u60F3\u4E86\u89E3\u66F4\u591A\u5173\u4E8E\u4F5C\u8005\u548C\u5176\u4ED6\u9879\u76EE\u7684\u4FE1\u606F\uFF1F"}),e.createEl("button",{text:"\u{1F310} \u8BBF\u95EE\u4F5C\u8005\u4E3B\u9875",cls:"mod-cta"}).addEventListener("click",()=>{window.open("https://ai.xinqi.life/about","_blank")})}addRewardSection(A){A.createEl("hr");let e=A.createDiv();e.createEl("h4",{text:"\u2615 \u652F\u6301\u4F5C\u8005"}),e.createEl("p",{text:"\u5982\u679C\u8FD9\u4E2A\u63D2\u4EF6\u5BF9\u60A8\u6709\u5E2E\u52A9\uFF0C\u6B22\u8FCE\u901A\u8FC7\u5FAE\u4FE1\u626B\u7801\u6253\u8D4F\u652F\u6301\u4F5C\u8005\u7EE7\u7EED\u5F00\u53D1\uFF01"});let t=e.createDiv();t.style.cssText=`
		text-align: center;
		margin: 16px 0;
		padding: 16px;
		background-color: var(--background-secondary);
		border-radius: 8px;
	`;let o=t.createEl("img");o.style.cssText=`
		max-width: 200px;
		max-height: 200px;
		width: auto;
		height: auto;
		border-radius: 4px;
	`,o.src="data:image/jpeg;base64,"+this.getRewardQRCodeBase64(),o.alt="\u5FAE\u4FE1\u6253\u8D4F\u4E8C\u7EF4\u7801";let s=t.createEl("p",{text:"\u5FAE\u4FE1\u626B\u4E00\u626B\uFF0C\u652F\u6301\u4F5C\u8005"});s.style.cssText=`
		margin-top: 8px;
		font-size: 14px;
		color: var(--text-muted);
	`}getRewardQRCodeBase64(){return"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"}startAutoAuth(){if(!this.plugin.settings.appId||!this.plugin.settings.appSecret){new R.Notice("\u274C \u8BF7\u5148\u914D\u7F6E App ID \u548C App Secret"),console.error("Missing App ID or App Secret");return}this.plugin.feishuApi.updateSettings(this.plugin.settings);try{let A=this.plugin.feishuApi.generateAuthUrl();window.open(A,"_blank"),new R.Notice("\u{1F504} \u5DF2\u6253\u5F00\u6D4F\u89C8\u5668\u8FDB\u884C\u6388\u6743\uFF0C\u5B8C\u6210\u540E\u5C06\u81EA\u52A8\u8FD4\u56DEObsidian");let e=()=>{this.display(),window.removeEventListener("feishu-auth-success",e)};window.addEventListener("feishu-auth-success",e)}catch(A){console.error("Auto auth error:",A),new R.Notice(`\u274C \u81EA\u52A8\u6388\u6743\u5931\u8D25: ${A.message}`)}}startManualAuth(){if(!this.plugin.settings.appId||!this.plugin.settings.appSecret){new R.Notice("\u274C \u8BF7\u5148\u914D\u7F6E App ID \u548C App Secret");return}try{this.plugin.feishuApi.updateSettings(this.plugin.settings),new j(this.app,this.plugin.feishuApi,async()=>{await this.plugin.saveSettings(),this.display()}).open()}catch(A){console.error("[Feishu Plugin] Failed to start manual auth:",A),new R.Notice("\u274C \u542F\u52A8\u6388\u6743\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5")}}showFolderSelectModal(){try{new S(this.app,this.plugin.feishuApi,async e=>{try{e?(this.plugin.settings.defaultFolderId=e.folder_token||e.token||"",this.plugin.settings.defaultFolderName=e.name):(console.log("[Feishu Plugin] Root folder selected (\u6211\u7684\u7A7A\u95F4)"),this.plugin.settings.defaultFolderId="",this.plugin.settings.defaultFolderName="\u6211\u7684\u7A7A\u95F4"),await this.plugin.saveSettings(),new R.Notice("\u2705 \u9ED8\u8BA4\u6587\u4EF6\u5939\u8BBE\u7F6E\u5DF2\u4FDD\u5B58"),this.display()}catch(t){console.error("[Feishu Plugin] Failed to save folder settings:",t),new R.Notice("\u274C \u4FDD\u5B58\u6587\u4EF6\u5939\u8BBE\u7F6E\u5931\u8D25")}}).open()}catch(A){console.error("[Feishu Plugin] Failed to open folder selection modal:",A),new R.Notice("\u274C \u6253\u5F00\u6587\u4EF6\u5939\u9009\u62E9\u5931\u8D25")}}};var f=require("obsidian");var P=class{constructor(i){this.localFiles=[];this.app=i}process(i){let A=i;return A=this.processWikiLinks(A),A=this.processBlockReferences(A),A=this.processTags(A),A=this.processEmbeds(A),A=this.processImages(A),A=this.cleanupWhitespace(A),A}processWikiLinks(i,A){return i.replace(/\[\[([^\]|]+)(\|([^\]]+))?\]\]/g,(e,t,o,s)=>{if(this.isFileReference(t)){let E=this.isImageFile(t);if(E?(A==null?void 0:A.enableLocalImageUpload)!==!1:(A==null?void 0:A.enableLocalAttachmentUpload)!==!1){let g=this.generatePlaceholder(),a={originalPath:t,fileName:this.extractFileName(t),placeholder:g,isImage:E,altText:s||t};return this.localFiles.push(a),g}else return e}else{let E=this.findLinkedMarkdownFile(t);if(E&&A&&A.enableSubDocumentUpload!==!1){let I=(0,f.normalizePath)(E.path);if(A.processedFiles.has(I))return r.warn(`\u26A0\uFE0F Circular reference detected for file: ${I}`),`\u{1F4DD} ${s||t} (\u5FAA\u73AF\u5F15\u7528)`;if(A.currentDepth>=A.maxDepth)return r.warn(`\u26A0\uFE0F Max depth reached for file: ${I}`),`\u{1F4DD} ${s||t} (\u6DF1\u5EA6\u9650\u5236)`;let g=this.generatePlaceholder(),a={originalPath:E.path,fileName:E.basename,placeholder:g,isImage:!1,isSubDocument:!0,altText:s||t};return this.localFiles.push(a),g}else return`\u{1F4DD} ${s||t}`}})}processBlockReferences(i){return i.replace(/\[\[([^#\]]+)#\^([^\]]+)\]\]/g,(A,e,t)=>`\u{1F4DD} ${e} (\u5757\u5F15\u7528: ${t})`)}processTags(i){return i.replace(/#([a-zA-Z0-9_\u4e00-\u9fff]+)/g,(A,e)=>`#${e}`)}processEmbeds(i,A){return i.replace(/!\[\[([^\]]+)\]\]/g,(e,t)=>{let o=this.isImageFile(t);if(o?(A==null?void 0:A.enableLocalImageUpload)!==!1:(A==null?void 0:A.enableLocalAttachmentUpload)!==!1){let E=this.generatePlaceholder(),I={originalPath:t,fileName:this.extractFileName(t),placeholder:E,isImage:o,altText:t};return this.localFiles.push(I),E}else return e})}processImages(i,A){return i.replace(/!\[([^\]]*)\]\(([^)]+)\)/g,(e,t,o)=>{if(o.startsWith("http://")||o.startsWith("https://"))return e;if((A==null?void 0:A.enableLocalImageUpload)!==!1){let s=this.generatePlaceholder(),E=t||"\u56FE\u7247",I={originalPath:o,fileName:this.extractFileName(o),placeholder:s,isImage:!0,altText:E};return this.localFiles.push(I),s}else return e})}cleanupWhitespace(i){return i=i.replace(/\n{3,}/g,`

`),i=i.replace(/[ \t]+$/gm,""),i=i.replace(/\s+$/,`
`),i}processCodeBlocks(i){return i.replace(/```(\w+)[\s\S]*?```/g,(A,e)=>A)}processMathFormulas(i){return i=i.replace(/\$([^$]+)\$/g,(A,e)=>`\u{1F4D0} \u6570\u5B66\u516C\u5F0F: ${e}`),i=i.replace(/\$\$([^$]+)\$\$/g,(A,e)=>`
\u{1F4D0} \u6570\u5B66\u516C\u5F0F\u5757:
${e}
`),i}processHighlights(i){return i.replace(/==([^=]+)==/g,(A,e)=>`<mark>${e}</mark>`)}processCallouts(i){let A=/^>\s*\[!([^\]]+)\](-?)\s*([^\n]*)\n((?:(?:>[^\n]*|)\n?)*?)(?=\n(?!>)|$)/gm;return i.replace(A,(e,t,o,s,E)=>{let I=t.toLowerCase().trim(),g=d[I]||d.default,a=s.trim()||g.title;a=this.escapeMarkdownInTitle(a);let c=E.split(`
`).map(h=>h.startsWith(">")?h.replace(/^>\s?/,""):h).filter((h,x,G)=>!(h===""&&x===G.length-1)).join(`
`),Q=`**${g.emoji} ${a}**`,Y=c.split(`
`).map(h=>h.trim()===""?">":`> ${h}`).join(`
`);return`
> ${Q}
>
${Y}

`})}escapeMarkdownInTitle(i){return i.replace(/\*\*/g,"*")}processComplete(i){let A=i;return A=this.processWikiLinks(A),A=this.processBlockReferences(A),A=this.processEmbeds(A),A=this.processImages(A),A=this.processTags(A),A=this.processHighlights(A),A=this.processMathFormulas(A),A=this.processCodeBlocks(A),A=this.cleanupWhitespace(A),A}processCompleteWithFiles(i,A=3,e="remove",t=!0,o=!0,s=!0){this.localFiles=[];let{content:E,frontMatter:I}=this.processFrontMatter(i,e),g={maxDepth:A,currentDepth:0,processedFiles:new Set,enableSubDocumentUpload:t,enableLocalImageUpload:o,enableLocalAttachmentUpload:s};return{content:this.processCompleteWithContext(E,g),localFiles:[...this.localFiles],frontMatter:I,extractedTitle:(I==null?void 0:I.title)||null}}generatePlaceholder(){let i=Date.now(),A=Math.random().toString(36).substring(2,8);return`__FEISHU_FILE_${i}_${A}__`}extractFileName(i){return i.split(/[/\\]/).pop()||i}isFileReference(i){let A=this.extractFileName(i);return A.includes(".")&&A.lastIndexOf(".")>0}isImageFile(i){let A=[".jpg",".jpeg",".png",".gif",".bmp",".svg",".webp"],e=i.toLowerCase().substring(i.lastIndexOf("."));return A.includes(e)}getLocalFiles(){return[...this.localFiles]}clearLocalFiles(){this.localFiles=[]}findLinkedMarkdownFile(i){var A;try{let e=i.trim();e=e.replace(/^\.\//,"").replace(/^\//,""),e.includes(".")||(e=e+".md");let t=(0,f.normalizePath)(e),o=this.app.vault.getFileByPath(t);if(!o){let s=(A=t.split("/").pop())==null?void 0:A.toLowerCase();s&&(o=this.app.vault.getMarkdownFiles().find(I=>I.name.toLowerCase()===s)||null)}if(!o){let s=i.trim().toLowerCase();o=this.app.vault.getMarkdownFiles().find(I=>I.basename.toLowerCase()===s)||null}return o?r.log(`\u2705 Found linked markdown file: "${i}" -> "${o.path}"`):r.log(`\u274C Linked markdown file not found: "${i}"`),o}catch(e){return r.error(`Error finding linked file for "${i}":`,e),null}}async processSubDocument(i,A){try{let e=(0,f.normalizePath)(i.path);A.processedFiles.add(e);let t=await this.app.vault.read(i),o={...A,currentDepth:A.currentDepth+1},s=[...this.localFiles];this.localFiles=[];let E=this.processCompleteWithContext(t,o),I=[...this.localFiles];return this.localFiles=s,{content:E,localFiles:I,frontMatter:null,extractedTitle:null}}catch(e){return r.error(`Error processing sub-document ${i.path}:`,e),{content:`\u274C \u65E0\u6CD5\u8BFB\u53D6\u5B50\u6587\u6863: ${i.basename}`,localFiles:[],frontMatter:null,extractedTitle:null}}}processCompleteWithContext(i,A){let e=i;return e=this.processCallouts(e),e=this.processWikiLinks(e,A),e=this.processBlockReferences(e),e=this.processEmbeds(e,A),e=this.processImages(e,A),e=this.processTags(e),e=this.processHighlights(e),e=this.processMathFormulas(e),e=this.processCodeBlocks(e),e=this.cleanupWhitespace(e),e}parseFrontMatter(i){if(!i.startsWith(`---
`)&&!i.startsWith(`---\r
`))return{frontMatter:null,content:i};let A=i.split(`
`),e=-1;for(let s=1;s<A.length;s++)if(A[s].trim()==="---"){e=s;break}if(e===-1)return{frontMatter:null,content:i};let t=A.slice(1,e).join(`
`),o=A.slice(e+1).join(`
`);try{return{frontMatter:this.parseSimpleYaml(t),content:o}}catch(s){return r.warn("Failed to parse Front Matter:",s),{frontMatter:null,content:i}}}parseSimpleYaml(i){let A={},e=i.split(`
`);for(let t of e){let o=t.trim();if(!o||o.startsWith("#"))continue;let s=o.indexOf(":");if(s===-1)continue;let E=o.substring(0,s).trim(),I=o.substring(s+1).trim();(I.startsWith('"')&&I.endsWith('"')||I.startsWith("'")&&I.endsWith("'"))&&(I=I.slice(1,-1)),A[E]=I}return A}processFrontMatter(i,A){let{frontMatter:e,content:t}=this.parseFrontMatter(i);if(!e)return{content:i,frontMatter:null};if(A==="remove")return{content:t,frontMatter:e};{let o=i.split(`
`),s=-1;for(let E=1;E<o.length;E++)if(o[E].trim()==="---"){s=E;break}if(s!==-1)return{content:"```yaml\n"+o.slice(1,s).join(`
`)+"\n```\n\n"+t,frontMatter:e}}return{content:t,frontMatter:e}}extractTitle(i,A,e){return e==="frontmatter"&&(A!=null&&A.title)?A.title:i}addShareMarkToFrontMatter(i,A){let e=new Date,o=new Date(e.getTime()+8*60*60*1e3).toISOString().replace("Z","+08:00"),{frontMatter:s,content:E}=this.parseFrontMatter(i),I={...s,feishushare:!0,feishu_url:A,feishu_shared_at:o},g=["---"];for(let[a,n]of Object.entries(I))n!=null&&(typeof n=="string"?n.includes(":")||n.includes("#")||n.includes("[")||n.includes("]")?g.push(`${a}: "${n}"`):g.push(`${a}: ${n}`):g.push(`${a}: ${n}`));return g.push("---"),g.join(`
`)+`
`+E}};var W=class extends M.Plugin{async onload(){await this.loadSettings(),this.feishuApi=new w(this.settings,this.app),this.markdownProcessor=new P(this.app),this.registerObsidianProtocolHandler("feishu-auth",A=>{this.handleOAuthCallback(A)}),this.addSettingTab(new D(this.app,this)),this.registerCommands(),this.registerMenus()}onunload(){}registerCommands(){this.addCommand({id:"share-current-note",name:"\u5206\u4EAB\u5F53\u524D\u7B14\u8BB0\u5230\u98DE\u4E66",editorCallback:(A,e)=>{this.shareCurrentNote()}})}registerMenus(){this.registerEvent(this.app.workspace.on("file-menu",(A,e)=>{e instanceof M.TFile&&e.extension==="md"&&A.addItem(t=>{t.setTitle("\u{1F4E4} \u5206\u4EAB\u5230\u98DE\u4E66").setIcon("share").onClick(()=>{this.shareFile(e)})})})),this.registerEvent(this.app.workspace.on("editor-menu",(A,e,t)=>{A.addItem(o=>{o.setTitle("\u{1F4E4} \u5206\u4EAB\u5230\u98DE\u4E66").setIcon("share").onClick(()=>{this.shareCurrentNote()})})}))}async loadSettings(){let A=await this.loadData();this.settings=Object.assign({},H,A)}async saveSettings(){await this.saveData(this.settings),this.feishuApi&&this.feishuApi.updateSettings(this.settings)}async handleOAuthCallback(A){if(this.log("Processing OAuth callback"),A.code){new M.Notice("\u{1F504} \u6B63\u5728\u5904\u7406\u6388\u6743\u56DE\u8C03...");try{await this.feishuApi.processCallback(`obsidian://feishu-auth?${new URLSearchParams(A).toString()}`)?(this.log("OAuth authorization successful"),new M.Notice("\u{1F389} \u81EA\u52A8\u6388\u6743\u6210\u529F\uFF01"),await this.saveSettings(),window.dispatchEvent(new CustomEvent("feishu-auth-success",{detail:{timestamp:Date.now(),source:"oauth-callback"}}))):(this.log("OAuth authorization failed","warn"),new M.Notice("\u274C \u6388\u6743\u5904\u7406\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5"))}catch(e){this.handleError(e,"OAuth\u56DE\u8C03\u5904\u7406")}}else if(A.error){let e=A.error_description||A.error;this.log(`OAuth error: ${e}`,"error"),new M.Notice(`\u274C \u6388\u6743\u5931\u8D25: ${e}`)}else this.log("Invalid OAuth callback parameters","warn"),new M.Notice("\u274C \u65E0\u6548\u7684\u6388\u6743\u56DE\u8C03")}async shareCurrentNote(){this.log("Attempting to share current note");let A=this.app.workspace.getActiveFile();if(!A){this.log("No active file found","warn"),new M.Notice("\u274C \u6CA1\u6709\u6253\u5F00\u7684\u7B14\u8BB0");return}if(A.extension!=="md"){this.log(`Unsupported file type: ${A.extension}`,"warn"),new M.Notice("\u274C \u53EA\u652F\u6301\u5206\u4EAB Markdown \u6587\u4EF6");return}this.log(`Sharing file: ${A.path}`),await this.shareFile(A)}async shareFile(A){this.log(`Starting file share process for: ${A.path}`);let e=new M.Notice("\u{1F504} \u6B63\u5728\u5206\u4EAB\u5230\u98DE\u4E66...",0);try{if(!this.settings.accessToken||!this.settings.userInfo){this.log("Authorization required","warn"),e.hide(),new M.Notice("\u274C \u8BF7\u5148\u5728\u8BBE\u7F6E\u4E2D\u5B8C\u6210\u98DE\u4E66\u6388\u6743");return}this.log("Reading file content");let t=await this.app.vault.read(A),o=this.markdownProcessor.processCompleteWithFiles(t,3,this.settings.frontMatterHandling,this.settings.enableSubDocumentUpload,this.settings.enableLocalImageUpload,this.settings.enableLocalAttachmentUpload),s=this.markdownProcessor.extractTitle(A.basename,o.frontMatter,this.settings.titleSource);this.log(`Processing file with title: ${s}`);let E=await this.feishuApi.shareMarkdownWithFiles(s,o,e);if(e.hide(),E.success){if(this.log(`File shared successfully: ${E.title}`),this.settings.enableShareMarkInFrontMatter&&E.url)try{this.log("Adding share mark to front matter");let I=this.markdownProcessor.addShareMarkToFrontMatter(t,E.url);await this.app.vault.modify(A,I),this.log("Share mark added successfully")}catch(I){this.log(`Failed to add share mark: ${I.message}`,"warn")}this.showSuccessNotification(E)}else this.log(`Share failed: ${E.error}`,"error"),new M.Notice(`\u274C \u5206\u4EAB\u5931\u8D25\uFF1A${E.error}`)}catch(t){e.hide(),this.handleError(t,"\u6587\u4EF6\u5206\u4EAB")}}async ensureValidAuth(){return!!this.settings.accessToken}showSuccessNotification(A){if(A.url){let e=`\u2705 \u5206\u4EAB\u6210\u529F\uFF01\u6587\u6863\uFF1A${A.title}`,o=new M.Notice(e,8e3).noticeEl.createEl("div");o.style.cssText=`
				display: flex;
				gap: 8px;
				margin-top: 8px;
			`;let s=o.createEl("button",{text:"\u{1F4CB} \u590D\u5236\u94FE\u63A5",cls:"mod-cta"});s.style.cssText="flex: 1;",s.onclick=async()=>{try{await navigator.clipboard.writeText(A.url),this.log("URL copied to clipboard"),s.textContent="\u2705 \u5DF2\u590D\u5236",setTimeout(()=>{s.textContent="\u{1F4CB} \u590D\u5236\u94FE\u63A5"},2e3)}catch(I){this.log(`Failed to copy URL: ${I.message}`,"error"),new M.Notice("\u274C \u590D\u5236\u5931\u8D25")}};let E=o.createEl("button",{text:"\u{1F517} \u6253\u5F00",cls:"mod-muted"});E.style.cssText="flex: 1;",E.onclick=()=>{A.url&&window.open(A.url,"_blank")}}else new M.Notice(`\u2705 \u5206\u4EAB\u6210\u529F\uFF01\u6587\u6863\u6807\u9898\uFF1A${A.title}`)}handleError(A,e,t){r.error(`${e}:`,A);let o=t||`\u274C ${e}\u5931\u8D25: ${A.message}`;new M.Notice(o)}log(A,e="info"){switch(e){case"error":r.error(A);break;case"warn":r.warn(A);break;default:r.log(A)}}};
