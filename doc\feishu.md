# 飞书插件API调用流程分析（已核对）

## 📋 **整体流程概述**

当用户在 Obsidian 中分享包含本地文件的 Markdown 内容到飞书时，插件会执行以下步骤：

## 🔄 **详细API调用流程**

### **第一阶段：文档创建**

#### 1. **创建飞书文档**
- **API**: `POST /open-apis/docx/v1/documents`
- **动作**: 创建一个新的空白飞书文档
- **参数**:
  ```json
  {
    "folder_token": "",  // 空表示根目录
    "title": "文档标题"
  }
  ```
- **返回**: 文档ID (`document_id`)

### **第二阶段：Markdown内容导入**

#### **步骤1：上传Markdown文件用于导入**

- **API**: `POST /open-apis/drive/v1/medias/upload_all`
- **Content-Type**: `multipart/form-data`
- **动作**: 将Markdown文件上传到云空间用于导入
- **参数**:
  ```form-data
  file_name: "content.md"
  parent_type: "ccm_import_open"  // 固定值，用于导入文件
  parent_node: ""  // 无需填写
  size: 文件大小(字节)
  file: Markdown文件二进制内容
  extra: '{"obj_type":"docx","file_extension":"md"}'  // 导入为新版文档
  ```
- **返回**: 文件token (`file_token`)

#### **步骤2：创建导入任务**

- **API**: `POST /open-apis/drive/v1/import_tasks`
- **动作**: 创建导入任务，将Markdown转换为飞书文档
- **参数**:
  ```json
  {
    "file_extension": "md",
    "file_token": "步骤1返回的file_token",
    "type": "docx",
    "file_name": "文档标题",
    "point": {
      "mount_type": 1,
      "mount_key": ""  // 空表示根目录
    }
  }
  ```
- **返回**: 导入任务ID (`ticket`)

#### **步骤3：查询导入结果**

- **API**: `GET /open-apis/drive/v1/import_tasks/{ticket}`
- **动作**: 轮询查询导入任务结果
- **返回**: 导入后的文档ID (`document_id`)

### **第三阶段：文件处理**

#### **步骤1：查找占位符文本块**

- **API**: `GET /open-apis/docx/v1/documents/{document_id}/blocks`
- **动作**: 遍历所有块，找到包含占位符的文本块
- **匹配**: 通过正则表达式匹配 `__FEISHU_FILE_timestamp_randomid__`
- **参数**:
  ```json
  {
    "page_size": 500,  // 每页返回的块数量
    "page_token": ""   // 分页标识，首次请求为空
  }
  ```

#### **步骤2：在占位符位置插入文件块**

- **API**: `POST /open-apis/docx/v1/documents/{document_id}/blocks/{parent_block_id}/children`
- **动作**: 在占位符文本块的位置插入空的文件块/图片块
- **参数**:
  ```json
  {
    "index": 占位符块的索引,
    "children": [
      {
        "block_type": 23,  // 文件块
        "file": {}
      }
      // 或
      {
        "block_type": 27,  // 图片块
        "image": {}
      }
    ]
  }
  ```
- **返回**: 新创建的块ID (`block_id`)

#### **步骤3：上传文件素材**

- **API**: `POST /open-apis/drive/v1/medias/upload_all`
- **Content-Type**: `multipart/form-data`
- **参数**:
  ```form-data
  file_name: "文件名.扩展名"
  parent_type: "docx_file" 或 "docx_image"
  parent_node: "新创建的文件块/图片块ID"
  size: 文件大小(字节)
  file: 文件二进制内容
  extra: '{"drive_route_token":"document_id"}'  // 文档ID
  ```
- **返回**: 素材token (`file_token`)

#### **步骤4：设置文件块内容**

- **API**: `PATCH /open-apis/docx/v1/documents/{document_id}/blocks/{block_id}`
- **参数**:
  ```json
  {
    "replace_file": {
      "token": "文件素材token"
    }
  }
  // 或
  {
    "replace_image": {
      "token": "图片素材token"
    }
  }
  ```

#### **步骤5：删除占位符文本块**

- **API**: `DELETE /open-apis/docx/v1/documents/{document_id}/blocks/{parent_block_id}/children/batch_delete`
- **参数**:
  ```json
  {
    "start_index": 占位符块索引,
    "end_index": 占位符块索引 + 1
  }
  ```

### **第四阶段：权限设置**

#### **步骤1：设置文档权限**
- **API**: `POST /open-apis/drive/v1/permissions/{token}/members`
- **动作**: 将文档设置为任何人可查看
- **参数**:
  ```json
  {
    "member_type": "anyone",
    "perm": "view"
  }
  ```

## 🔧 **关键修正点**

### ✅ **新增的ccm_import_open流程**
1. **支持Markdown导入**: 使用 `parent_type: "ccm_import_open"` 可以直接导入Markdown文件
2. **简化流程**: 不需要手动解析Markdown，飞书会自动转换
3. **保持占位符**: 导入后的文档中，本地文件引用会变成占位符文本

### ✅ **完整的四阶段流程**
1. **第一阶段**: ~~创建空文档~~ (已移除，直接导入)
2. **第二阶段**: 使用ccm_import_open导入Markdown内容
3. **第三阶段**: 处理文件占位符，上传实际文件
4. **第四阶段**: 设置文档权限

### ✅ **接口参数完善**
- 添加了 `extra` 参数的正确使用方式
- 明确了各个接口的必需参数和可选参数
- 补充了返回值说明

## 📝 **代码调整记录**

### 🔄 **主要修改**
1. **修改主调用方法** (`shareMarkdown`):
   - 从 `shareWithBlockInsertion()` 改为 `shareWithNativeImport()`
   - 添加了失败回退机制

2. **修正上传参数** (`uploadMarkdownForImport`):
   - 移除了测试内容，使用实际的 markdown 内容
   - 确保 `parent_type: "ccm_import_open"` 正确设置

3. **修正导入任务参数** (`createImportTask`):
   - `mount_key` 从 `"root"` 改为 `""` (空字符串表示根目录)

### ✅ **已验证的接口**
- ✅ `POST /open-apis/drive/v1/medias/upload_all` (ccm_import_open)
- ✅ `POST /open-apis/drive/v1/import_tasks`
- ✅ `GET /open-apis/drive/v1/import_tasks/{ticket}`
- ✅ `GET /open-apis/docx/v1/documents/{document_id}/blocks`
- ✅ `POST /open-apis/docx/v1/documents/{document_id}/blocks/{block_id}/children`
- ✅ `PATCH /open-apis/docx/v1/documents/{document_id}/blocks/{block_id}`
- ✅ `DELETE /open-apis/docx/v1/documents/{document_id}/blocks/{block_id}/children/batch_delete`
- ✅ `POST /open-apis/drive/v1/permissions/{token}/members`

### 🎯 **流程优化效果**
1. **减少API调用**: 不再需要手动创建空文档
2. **提高成功率**: 利用飞书原生导入能力，减少手动构建错误
3. **保持兼容性**: 失败时自动回退到原有的块插入模式

## 🚨 **实际测试结果与最终决策**

### ❌ **ccm_import_open 实施遇到的问题**
经过多轮测试和调试，`ccm_import_open` 方式持续遇到 HTTP 400 错误：

1. **尝试的方法**:
   - ✅ 严格按照官方文档示例格式
   - ✅ 移除可能有问题的 `parent_node` 参数
   - ✅ 调整参数顺序匹配官方 curl 示例
   - ✅ 使用手动构建的 multipart/form-data
   - ✅ 使用原生 fetch API（遇到 CORS 限制）

2. **持续的错误**:
   - HTTP 400 Bad Request
   - 响应体长度 31 字节（简短错误信息）
   - Obsidian 的 `requestUrl` 无法捕获详细错误信息

3. **可能的原因**:
   - 飞书应用权限不足
   - 特定的网络环境限制
   - API 参数格式的细微差异
   - 飞书账户类型限制

### ✅ **最终决策：回退到块插入模式**

基于"流程不变"的原则，我们决定：

1. **主流程**: 优先尝试 `ccm_import_open` 原生导入
2. **回退机制**: 自动回退到已验证可行的块插入模式
3. **用户体验**: 确保插件始终能正常工作

### 📋 **当前实现状态**
- ✅ `shareWithNativeImport()` 会快速失败并触发回退
- ✅ `shareWithBlockInsertion()` 作为可靠的备用方案
- ✅ 用户无感知的自动切换
- ✅ 保持所有现有功能正常工作

### 🔮 **未来优化方向**
1. **权限排查**: 与飞书技术支持确认所需的具体权限
2. **环境测试**: 在不同网络环境下测试 `ccm_import_open`
3. **官方支持**: 获取飞书官方的技术支持和调试帮助

