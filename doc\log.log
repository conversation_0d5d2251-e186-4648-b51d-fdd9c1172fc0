plugin:feishu-share:113 [Feishu Plugin] Starting file share process for: AI 入门第一课：从零打造你的专属微信表情包.md
plugin:feishu-share:113 [Feishu Plugin] Reading file content
plugin:feishu-share:103 🎨 Generated callout placeholder: {{FEISHU_CALLOUT_1754751545646_xhxo3s}} for type: note
plugin:feishu-share:103 🎨 Generated callout placeholder: {{FEISHU_CALLOUT_1754751545646_wofzzl}} for type: tip
plugin:feishu-share:103 🎨 Generated callout placeholder: {{FEISHU_CALLOUT_1754751545646_fkdfsm}} for type: example
plugin:feishu-share:103 🎨 Generated callout placeholder: {{FEISHU_CALLOUT_1754751545646_0ae93l}} for type: abstract
plugin:feishu-share:103 🎨 Generated callout placeholder: {{FEISHU_CALLOUT_1754751545646_566kek}} for type: info
plugin:feishu-share:103 🎨 Generated callout placeholder: {{FEISHU_CALLOUT_1754751545646_5qqz0s}} for type: tip
plugin:feishu-share:103 🎨 Generated callout placeholder: {{FEISHU_CALLOUT_1754751545646_ewd5p7}} for type: warning
plugin:feishu-share:103 🎨 Generated callout placeholder: {{FEISHU_CALLOUT_1754751545646_rwtwht}} for type: bonus
plugin:feishu-share:103 🎨 Generated callout placeholder: {{FEISHU_CALLOUT_1754751545646_lerqwf}} for type: success
plugin:feishu-share:103 🎨 Generated callout placeholder: {{FEISHU_CALLOUT_1754751545646_xydxj8}} for type: note
plugin:feishu-share:113 [Feishu Plugin] Processing file with title: AI 入门第一课：从零打造你的专属微信表情包
plugin:feishu-share:6 📁 Upload: Using root folder (我的空间) - no parent_node specified
plugin:feishu-share:10 ✅ Import: Using default root folder (我的空间)
plugin:feishu-share:10 Import task request: {
  "file_extension": "md",
  "file_token": "LI7jbIKtyom5rDxHUgkc0dUKnGb",
  "type": "docx",
  "file_name": "AI 入门第一课：从零打造你的专属微信表情包",
  "point": {
    "mount_type": 1,
    "mount_key": "nodcn2EG5YG1i5Rsh5uZs0FsUje"
  }
}
plugin:feishu-share:10 Import task response: {
  "code": 0,
  "data": {
    "ticket": "7536600505817874434"
  },
  "msg": "success"
}
plugin:feishu-share:6 Step 3: Waiting for import completion (15s timeout)...
plugin:feishu-share:10 🚀 [NEW CODE] Starting import completion check with ticket: 7536600505817874434, timeout: 15000ms
plugin:feishu-share:10 📋 Import status response: {
  "code": 0,
  "data": {
    "result": {
      "extra": [],
      "job_error_msg": "",
      "job_status": 2
    }
  },
  "msg": "success"
}
plugin:feishu-share:10 📊 Import status: 2, token: none
plugin:feishu-share:10 🔍 Status 2 detected. Document token: none
plugin:feishu-share:10 ⚠️ Import shows failure status (2), no document token yet. Attempt 1/8, continuing to wait...
waitForImportCompletionWithTimeout @ plugin:feishu-share:10
await in waitForImportCompletionWithTimeout
shareMarkdownWithFiles @ plugin:feishu-share:6
await in shareMarkdownWithFiles
shareFile @ plugin:feishu-share:113
await in shareFile
eval @ plugin:feishu-share:113
e.handleEvent @ app.js:1
plugin:feishu-share:10 📋 Import status response: {
  "code": 0,
  "data": {
    "result": {
      "extra": [],
      "job_error_msg": "",
      "job_status": 2
    }
  },
  "msg": "success"
}
plugin:feishu-share:10 📊 Import status: 2, token: none
plugin:feishu-share:10 🔍 Status 2 detected. Document token: none
plugin:feishu-share:10 ⚠️ Import shows failure status (2), no document token yet. Attempt 2/8, continuing to wait...
waitForImportCompletionWithTimeout @ plugin:feishu-share:10
await in waitForImportCompletionWithTimeout
shareMarkdownWithFiles @ plugin:feishu-share:6
await in shareMarkdownWithFiles
shareFile @ plugin:feishu-share:113
await in shareFile
eval @ plugin:feishu-share:113
e.handleEvent @ app.js:1
plugin:feishu-share:10 📋 Import status response: {
  "code": 0,
  "data": {
    "result": {
      "extra": [],
      "job_error_msg": "",
      "job_status": 2
    }
  },
  "msg": "success"
}
plugin:feishu-share:10 📊 Import status: 2, token: none
plugin:feishu-share:10 🔍 Status 2 detected. Document token: none
plugin:feishu-share:10 ⚠️ Import shows failure status (2), no document token yet. Attempt 3/8, continuing to wait...
waitForImportCompletionWithTimeout @ plugin:feishu-share:10
await in waitForImportCompletionWithTimeout
shareMarkdownWithFiles @ plugin:feishu-share:6
await in shareMarkdownWithFiles
shareFile @ plugin:feishu-share:113
await in shareFile
eval @ plugin:feishu-share:113
e.handleEvent @ app.js:1
plugin:feishu-share:10 📋 Import status response: {
  "code": 0,
  "data": {
    "result": {
      "extra": [],
      "job_error_msg": "",
      "job_status": 2
    }
  },
  "msg": "success"
}
plugin:feishu-share:10 📊 Import status: 2, token: none
plugin:feishu-share:10 🔍 Status 2 detected. Document token: none
plugin:feishu-share:10 ⚠️ Import shows failure status (2), no document token yet. Attempt 4/8, continuing to wait...
waitForImportCompletionWithTimeout @ plugin:feishu-share:10
await in waitForImportCompletionWithTimeout
shareMarkdownWithFiles @ plugin:feishu-share:6
await in shareMarkdownWithFiles
shareFile @ plugin:feishu-share:113
await in shareFile
eval @ plugin:feishu-share:113
e.handleEvent @ app.js:1
plugin:feishu-share:10 📋 Import status response: {
  "code": 0,
  "data": {
    "result": {
      "extra": [
        "_pod_name"
      ],
      "job_error_msg": "success",
      "job_status": 0,
      "token": "GAjqd136voezpNxfZPAc3JWrnwG",
      "type": "docx",
      "url": "https://l0c34idk7v.feishu.cn/docx/GAjqd136voezpNxfZPAc3JWrnwG"
    }
  },
  "msg": "success"
}
plugin:feishu-share:10 📊 Import status: 0, token: GAjqd136voezpNxfZPAc3JWrnwG
plugin:feishu-share:14 🎨 Processing 10 callout blocks...
plugin:feishu-share:14 🔍 Searching for callout placeholder: {{FEISHU_CALLOUT_1754751545646_xhxo3s}}
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_xhxo3s}}"
plugin:feishu-share:14 ✅ Found callout placeholder match: "{{FEISHU_CALLOUT_1754751545646_xhxo3s}}" in "{{FEISHU_CALLOUT_1754751545646_xhxo3s}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_wofzzl}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_fkdfsm}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_0ae93l}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_566kek}}
"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_5qqz0s}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_ewd5p7}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_rwtwht}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_lerqwf}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_xydxj8}}"
plugin:feishu-share:14 🔍 Found 1 callout placeholder blocks
plugin:feishu-share:14 🔍 Found placeholder block for callout: doxcnvctl9WaGkzbTocIhLp3Ptb
plugin:feishu-share:14 🔧 Creating callout block with data: {block_type: 19, callout: {…}}
plugin:feishu-share:14 📋 Insert callout response: {code: 0, data: {…}, msg: 'success'}
plugin:feishu-share:14 ✅ Created callout block: doxcn7YeTaFb4grQ0G7jv7VXkrc
plugin:feishu-share:14 📋 Add callout content response: {code: 0, data: {…}, msg: 'success'}
plugin:feishu-share:14 ✅ Added content to callout block: doxcn7YeTaFb4grQ0G7jv7VXkrc
plugin:feishu-share:14 ✅ Deleted placeholder block: doxcnvctl9WaGkzbTocIhLp3Ptb
plugin:feishu-share:14 ✅ Successfully processed callout: note
plugin:feishu-share:14 🔍 Searching for callout placeholder: {{FEISHU_CALLOUT_1754751545646_wofzzl}}
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_xhxo3s}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_wofzzl}}"
plugin:feishu-share:14 ✅ Found callout placeholder match: "{{FEISHU_CALLOUT_1754751545646_wofzzl}}" in "{{FEISHU_CALLOUT_1754751545646_wofzzl}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_fkdfsm}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_0ae93l}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_566kek}}
"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_5qqz0s}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_ewd5p7}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_rwtwht}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_lerqwf}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_xydxj8}}"
plugin:feishu-share:14 🔍 Found 1 callout placeholder blocks
plugin:feishu-share:14 🔍 Found placeholder block for callout: doxcnLWoVdkaNZ3uoEMjGkG2jwg
plugin:feishu-share:14 🔧 Creating callout block with data: {block_type: 19, callout: {…}}
plugin:feishu-share:14 📋 Insert callout response: {code: 0, data: {…}, msg: 'success'}
plugin:feishu-share:14 ✅ Created callout block: doxcnGmtCGNnxTDhLkbzP1IS6CE
plugin:feishu-share:14 📋 Add callout content response: {code: 0, data: {…}, msg: 'success'}
plugin:feishu-share:14 ✅ Added content to callout block: doxcnGmtCGNnxTDhLkbzP1IS6CE
plugin:feishu-share:14 ✅ Deleted placeholder block: doxcnLWoVdkaNZ3uoEMjGkG2jwg
plugin:feishu-share:14 ✅ Successfully processed callout: tip
plugin:feishu-share:14 🔍 Searching for callout placeholder: {{FEISHU_CALLOUT_1754751545646_fkdfsm}}
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_xhxo3s}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_wofzzl}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_fkdfsm}}"
plugin:feishu-share:14 ✅ Found callout placeholder match: "{{FEISHU_CALLOUT_1754751545646_fkdfsm}}" in "{{FEISHU_CALLOUT_1754751545646_fkdfsm}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_0ae93l}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_566kek}}
"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_5qqz0s}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_ewd5p7}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_rwtwht}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_lerqwf}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_xydxj8}}"
plugin:feishu-share:14 🔍 Found 1 callout placeholder blocks
plugin:feishu-share:14 🔍 Found placeholder block for callout: doxcnfpWI2EjMQlDTb5ImaWqGlc
plugin:feishu-share:14 🔧 Creating callout block with data: {block_type: 19, callout: {…}}
plugin:feishu-share:14 📋 Insert callout response: {code: 0, data: {…}, msg: 'success'}
plugin:feishu-share:14 ✅ Created callout block: doxcnJuQgYySInEkCE0c7ZxTIDc
plugin:feishu-share:14 📋 Add callout content response: {code: 0, data: {…}, msg: 'success'}
plugin:feishu-share:14 ✅ Added content to callout block: doxcnJuQgYySInEkCE0c7ZxTIDc
plugin:feishu-share:14 ✅ Deleted placeholder block: doxcnfpWI2EjMQlDTb5ImaWqGlc
plugin:feishu-share:14 ✅ Successfully processed callout: example
plugin:feishu-share:14 🔍 Searching for callout placeholder: {{FEISHU_CALLOUT_1754751545646_0ae93l}}
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_xhxo3s}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_wofzzl}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_fkdfsm}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_0ae93l}}"
plugin:feishu-share:14 ✅ Found callout placeholder match: "{{FEISHU_CALLOUT_1754751545646_0ae93l}}" in "{{FEISHU_CALLOUT_1754751545646_0ae93l}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_566kek}}
"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_5qqz0s}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_ewd5p7}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_rwtwht}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_lerqwf}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_xydxj8}}"
plugin:feishu-share:14 🔍 Found 1 callout placeholder blocks
plugin:feishu-share:14 🔍 Found placeholder block for callout: doxcn5XJKtzOLYzcoallmGiBiLb
plugin:feishu-share:14 🔧 Creating callout block with data: {block_type: 19, callout: {…}}
plugin:feishu-share:14 📋 Insert callout response: {code: 0, data: {…}, msg: 'success'}
plugin:feishu-share:14 ✅ Created callout block: doxcnSENfOnjBMIvUKEACTPGNXe
plugin:feishu-share:14 📋 Add callout content response: {code: 0, data: {…}, msg: 'success'}
plugin:feishu-share:14 ✅ Added content to callout block: doxcnSENfOnjBMIvUKEACTPGNXe
plugin:feishu-share:14 ✅ Deleted placeholder block: doxcn5XJKtzOLYzcoallmGiBiLb
plugin:feishu-share:14 ✅ Successfully processed callout: abstract
plugin:feishu-share:14 🔍 Searching for callout placeholder: {{FEISHU_CALLOUT_1754751545646_566kek}}
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_xhxo3s}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_wofzzl}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_fkdfsm}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_0ae93l}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_566kek}}
"
plugin:feishu-share:14 ✅ Found callout placeholder match: "{{FEISHU_CALLOUT_1754751545646_566kek}}" in "{{FEISHU_CALLOUT_1754751545646_566kek}}
"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_5qqz0s}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_ewd5p7}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_rwtwht}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_lerqwf}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_xydxj8}}"
plugin:feishu-share:14 🔍 Found 1 callout placeholder blocks
plugin:feishu-share:14 🔍 Found placeholder block for callout: doxcnYKXHivtRI6JNTmQI2V8Xfd
plugin:feishu-share:14 🔧 Creating callout block with data: {block_type: 19, callout: {…}}
plugin:feishu-share:14 📋 Insert callout response: {code: 0, data: {…}, msg: 'success'}
plugin:feishu-share:14 ✅ Created callout block: doxcn2JnWkjLNRN74FBBc8sXnXg
plugin:feishu-share:14 📋 Add callout content response: {code: 0, data: {…}, msg: 'success'}
plugin:feishu-share:14 ✅ Added content to callout block: doxcn2JnWkjLNRN74FBBc8sXnXg
plugin:feishu-share:14 ✅ Deleted placeholder block: doxcnYKXHivtRI6JNTmQI2V8Xfd
plugin:feishu-share:14 ✅ Successfully processed callout: info
plugin:feishu-share:14 🔍 Searching for callout placeholder: {{FEISHU_CALLOUT_1754751545646_5qqz0s}}
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_xhxo3s}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_wofzzl}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_fkdfsm}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_0ae93l}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_566kek}}
"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_5qqz0s}}"
plugin:feishu-share:14 ✅ Found callout placeholder match: "{{FEISHU_CALLOUT_1754751545646_5qqz0s}}" in "{{FEISHU_CALLOUT_1754751545646_5qqz0s}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_ewd5p7}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_rwtwht}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_lerqwf}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_xydxj8}}"
plugin:feishu-share:14 🔍 Found 1 callout placeholder blocks
plugin:feishu-share:14 🔍 Found placeholder block for callout: doxcntKCO9UAzCpTzPfGIOgEvVf
plugin:feishu-share:14 🔧 Creating callout block with data: {block_type: 19, callout: {…}}
plugin:feishu-share:14 📋 Insert callout response: {code: 0, data: {…}, msg: 'success'}
plugin:feishu-share:14 ✅ Created callout block: doxcnPYl5bLtis5E1trgPOHIJjc
plugin:feishu-share:14 📋 Add callout content response: {code: 0, data: {…}, msg: 'success'}
plugin:feishu-share:14 ✅ Added content to callout block: doxcnPYl5bLtis5E1trgPOHIJjc
plugin:feishu-share:14 ✅ Deleted placeholder block: doxcntKCO9UAzCpTzPfGIOgEvVf
plugin:feishu-share:14 ✅ Successfully processed callout: tip
plugin:feishu-share:14 🔍 Searching for callout placeholder: {{FEISHU_CALLOUT_1754751545646_ewd5p7}}
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_xhxo3s}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_wofzzl}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_fkdfsm}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_0ae93l}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_566kek}}
"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_5qqz0s}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_ewd5p7}}"
plugin:feishu-share:14 ✅ Found callout placeholder match: "{{FEISHU_CALLOUT_1754751545646_ewd5p7}}" in "{{FEISHU_CALLOUT_1754751545646_ewd5p7}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_rwtwht}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_lerqwf}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_xydxj8}}"
plugin:feishu-share:14 🔍 Found 1 callout placeholder blocks
plugin:feishu-share:14 🔍 Found placeholder block for callout: doxcnGxwi7FK76s0P2ASekOWCSe
plugin:feishu-share:14 🔧 Creating callout block with data: {block_type: 19, callout: {…}}
plugin:feishu-share:14 📋 Insert callout response: {code: 0, data: {…}, msg: 'success'}
plugin:feishu-share:14 ✅ Created callout block: doxcn5qZ68qJ76UzwFcHvizs1Cg
plugin:feishu-share:14 📋 Add callout content response: {code: 0, data: {…}, msg: 'success'}
plugin:feishu-share:14 ✅ Added content to callout block: doxcn5qZ68qJ76UzwFcHvizs1Cg
plugin:feishu-share:14 ✅ Deleted placeholder block: doxcnGxwi7FK76s0P2ASekOWCSe
plugin:feishu-share:14 ✅ Successfully processed callout: warning
plugin:feishu-share:14 🔍 Searching for callout placeholder: {{FEISHU_CALLOUT_1754751545646_rwtwht}}
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_xhxo3s}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_wofzzl}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_fkdfsm}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_0ae93l}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_566kek}}
"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_5qqz0s}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_ewd5p7}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_rwtwht}}"
plugin:feishu-share:14 ✅ Found callout placeholder match: "{{FEISHU_CALLOUT_1754751545646_rwtwht}}" in "{{FEISHU_CALLOUT_1754751545646_rwtwht}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_lerqwf}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_xydxj8}}"
plugin:feishu-share:14 🔍 Found 1 callout placeholder blocks
plugin:feishu-share:14 🔍 Found placeholder block for callout: doxcnoXmHJYRRm1env7DwikTy6g
plugin:feishu-share:14 🔧 Creating callout block with data: {block_type: 19, callout: {…}}
plugin:feishu-share:14 📋 Insert callout response: {code: 0, data: {…}, msg: 'success'}
plugin:feishu-share:14 ✅ Created callout block: doxcnEhSgZ7IkVxeUvzsI28uCBd
plugin:feishu-share:14 📋 Add callout content response: {code: 0, data: {…}, msg: 'success'}
plugin:feishu-share:14 ✅ Added content to callout block: doxcnEhSgZ7IkVxeUvzsI28uCBd
plugin:feishu-share:14 ✅ Deleted placeholder block: doxcnoXmHJYRRm1env7DwikTy6g
plugin:feishu-share:14 ✅ Successfully processed callout: bonus
plugin:feishu-share:14 🔍 Searching for callout placeholder: {{FEISHU_CALLOUT_1754751545646_lerqwf}}
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_xhxo3s}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_wofzzl}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_fkdfsm}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_0ae93l}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_566kek}}
"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_5qqz0s}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_ewd5p7}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_rwtwht}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_lerqwf}}"
plugin:feishu-share:14 ✅ Found callout placeholder match: "{{FEISHU_CALLOUT_1754751545646_lerqwf}}" in "{{FEISHU_CALLOUT_1754751545646_lerqwf}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_xydxj8}}"
plugin:feishu-share:14 🔍 Found 1 callout placeholder blocks
plugin:feishu-share:14 🔍 Found placeholder block for callout: doxcnwNOaweGRaKT5JTbwgVZaKc
plugin:feishu-share:14 🔧 Creating callout block with data: {block_type: 19, callout: {…}}
plugin:feishu-share:14 📋 Insert callout response: {code: 0, data: {…}, msg: 'success'}
plugin:feishu-share:14 ✅ Created callout block: doxcn1y4c7vbETdv6rvHnfJSLuf
plugin:feishu-share:14 📋 Add callout content response: {code: 0, data: {…}, msg: 'success'}
plugin:feishu-share:14 ✅ Added content to callout block: doxcn1y4c7vbETdv6rvHnfJSLuf
plugin:feishu-share:14 ✅ Deleted placeholder block: doxcnwNOaweGRaKT5JTbwgVZaKc
plugin:feishu-share:14 ✅ Successfully processed callout: success
plugin:feishu-share:14 🔍 Searching for callout placeholder: {{FEISHU_CALLOUT_1754751545646_xydxj8}}
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_xhxo3s}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_wofzzl}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_fkdfsm}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_0ae93l}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_566kek}}
"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_5qqz0s}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_ewd5p7}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_rwtwht}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_lerqwf}}"
plugin:feishu-share:14 🔍 Found potential callout content: "{{FEISHU_CALLOUT_1754751545646_xydxj8}}"
plugin:feishu-share:14 ✅ Found callout placeholder match: "{{FEISHU_CALLOUT_1754751545646_xydxj8}}" in "{{FEISHU_CALLOUT_1754751545646_xydxj8}}"
plugin:feishu-share:14 🔍 Found 1 callout placeholder blocks
plugin:feishu-share:14 🔍 Found placeholder block for callout: doxcnmAkzk6TuKWUo5B6jLA9PSc
plugin:feishu-share:14 🔧 Creating callout block with data: {block_type: 19, callout: {…}}
plugin:feishu-share:14 📋 Insert callout response: {code: 0, data: {…}, msg: 'success'}
plugin:feishu-share:14 ✅ Created callout block: doxcnLpPFCi8lrpPjT1TiXDslzc
plugin:feishu-share:14 📋 Add callout content response: {code: 0, data: {…}, msg: 'success'}
plugin:feishu-share:14 ✅ Added content to callout block: doxcnLpPFCi8lrpPjT1TiXDslzc
plugin:feishu-share:14 ✅ Deleted placeholder block: doxcnmAkzk6TuKWUo5B6jLA9PSc
plugin:feishu-share:14 ✅ Successfully processed callout: note
plugin:feishu-share:14 🎨 Completed processing 10 callout blocks
plugin:feishu-share:10 ⚠️ Trash method failed, trying direct delete...
deleteSourceFile @ plugin:feishu-share:10
await in deleteSourceFile
shareMarkdownWithFiles @ plugin:feishu-share:6
await in shareMarkdownWithFiles
shareFile @ plugin:feishu-share:113
await in shareFile
eval @ plugin:feishu-share:113
e.handleEvent @ app.js:1
plugin:feishu-share:113 [Feishu Plugin] File shared successfully: AI 入门第一课：从零打造你的专属微信表情包