# 测试分享标记功能

这是一个测试文档，用来验证分享到飞书后自动添加frontmatter标记的功能。

## 功能说明

当启用"自动添加分享标记"功能后，分享成功时会自动在文档的frontmatter中添加：

- `feishushare: true` - 标记文档已分享
- `feishu_url: "分享链接"` - 记录分享的URL
- `feishu_shared_at: "时间戳"` - 记录分享时间

## 测试步骤

1. 在插件设置中启用"自动添加分享标记"
2. 分享这个文档到飞书
3. 检查文档顶部是否自动添加了相应的frontmatter字段

## 预期结果

分享成功后，文档顶部应该会出现类似这样的frontmatter：

```yaml
---
feishushare: true
feishu_url: "https://example.feishu.cn/docs/xxx"
feishu_shared_at: "2024-01-10T10:30:00.000Z"
---
```
