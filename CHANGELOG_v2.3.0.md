# 版本更新日志 v2.3.0

## 🎉 新功能：自动分享标记

### 📋 功能概述
新增了"自动分享标记"功能，可以在分享成功后自动在笔记的 Front Matter 中添加分享相关的标记信息。

### ✨ 主要特性

#### 1. 自动标记主文档
- ✅ 分享成功后自动添加 `feishushare: true` 标记
- 🔗 记录分享链接 `feishu_url`
- ⏰ 记录分享时间 `feishu_shared_at`（东8区时间）
- 🔄 重复分享时自动更新时间戳

#### 2. 子文档支持
- 📄 子文档（嵌入的Markdown文件）也会自动添加分享标记
- 🔗 每个子文档都有独立的分享链接和时间戳
- 📝 子文档标记添加失败不影响主分享流程

#### 3. 用户控制
- ⚙️ 功能默认开启，可在设置中关闭
- 🛡️ 只有获取到分享URL才会添加标记
- 🔒 分享失败时不会添加任何标记

### 🔧 技术实现

#### 1. 设置项
- 新增 `enableShareMarkInFrontMatter` 设置项
- 默认值为 `true`（开启状态）

#### 2. 时间格式
- 使用东8区时间：`2024-01-10T18:30:00.000+08:00`
- 替代原来的UTC时间格式

#### 3. Front Matter 字段
```yaml
---
feishushare: true
feishu_url: "https://example.feishu.cn/docs/xxx"
feishu_shared_at: "2024-01-10T18:30:00.000+08:00"
---
```

### 📝 使用方法

#### 启用/禁用功能
1. 打开 Obsidian 设置
2. 进入"飞书分享"插件设置
3. 在"内容处理设置"部分找到"自动添加分享标记"
4. 开启或关闭此开关（默认开启）

#### 正常分享
按照正常流程分享笔记到飞书，分享成功后会自动添加标记。

### 🔄 升级说明

#### 对现有用户的影响
- 功能默认开启，如不需要可手动关闭
- 不影响现有的分享流程
- 已分享的文档不会自动添加标记，只有重新分享才会添加

#### 向后兼容性
- 完全向后兼容，不会破坏现有功能
- 新功能是可选的，不会强制使用

### 🐛 修复和优化

#### 1. 时间显示优化
- 修改为东8区时间显示
- 更符合中国用户的使用习惯

#### 2. 错误处理增强
- 标记添加失败不影响主分享流程
- 详细的日志记录便于调试

#### 3. 代码结构优化
- 在 `FeishuApiService` 中集成 `MarkdownProcessor`
- 统一的标记添加逻辑

### 📊 版本信息
- **版本号**: v2.3.0
- **发布日期**: 2024年1月
- **兼容性**: Obsidian 0.15.0+
- **构建状态**: ✅ 通过

### 🔮 后续计划
- 考虑添加批量管理已分享文档的功能
- 支持自定义标记字段名称
- 添加分享历史记录功能

---

**感谢使用飞书分享插件！如有问题或建议，欢迎反馈。**
