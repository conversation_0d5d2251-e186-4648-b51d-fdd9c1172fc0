# 飞书API接口调用文档

## 📋 概述

本文档详细说明了 obsidian-feishu-direct 插件中所有飞书API的调用方式、参数、响应格式和错误处理机制。

## 🔐 授权相关API

### 1. OAuth授权URL生成

**方法**: `generateAuthUrl()`  
**用途**: 生成飞书OAuth授权链接

```typescript
generateAuthUrl(): string {
    const state = Math.random().toString(36).substring(2, 15);
    localStorage.setItem('feishu-oauth-state', state);
    
    const params = new URLSearchParams({
        app_id: this.settings.appId,
        redirect_uri: this.settings.callbackUrl,
        response_type: 'code',
        scope: 'docs:read docs:write drive:read drive:write',
        state: state
    });
    
    return `https://open.feishu.cn/open-apis/authen/v1/authorize?${params.toString()}`;
}
```

**返回值**: 完整的授权URL字符串

### 2. 获取访问令牌

**API端点**: `POST https://open.feishu.cn/open-apis/authen/v1/oidc/access_token`  
**方法**: `getAccessToken(code: string)`

**请求参数**:
```typescript
{
    grant_type: "authorization_code",
    code: string,           // 授权码
    redirect_uri: string    // 回调地址
}
```

**请求头**:
```typescript
{
    'Content-Type': 'application/json',
    'Authorization': `Basic ${base64(appId:appSecret)}`
}
```

**响应格式**:
```typescript
interface FeishuOAuthResponse {
    code: number;
    msg: string;
    data: {
        access_token: string;
        refresh_token: string;
        expires_in: number;
        token_type: string;
    };
}
```

### 3. 刷新访问令牌

**API端点**: `POST https://open.feishu.cn/open-apis/authen/v1/oidc/refresh_access_token`  
**方法**: `refreshAccessToken()`

**请求参数**:
```typescript
{
    grant_type: "refresh_token",
    refresh_token: string
}
```

**错误处理**:
- 99991665: refresh_token无效
- 99991666: refresh_token已过期

### 4. 获取用户信息

**API端点**: `GET https://open.feishu.cn/open-apis/authen/v1/user_info`  
**方法**: `getUserInfo()`

**请求头**:
```typescript
{
    'Authorization': `Bearer ${accessToken}`
}
```

**响应格式**:
```typescript
interface FeishuUserInfo {
    name: string;
    avatar_url: string;
    email: string;
    user_id: string;
}
```

## 📄 文档相关API

### 1. 上传Markdown文件

**API端点**: `POST https://open.feishu.cn/open-apis/drive/v1/files/upload_all`  
**方法**: `uploadMarkdownFile(title: string, content: string)`

**请求格式**: `multipart/form-data`
```typescript
{
    file_name: string,      // 文件名（含.md扩展名）
    parent_type: "explorer",
    parent_node: string,    // 父文件夹token
    size: number,           // 文件大小
    file: Blob             // 文件内容
}
```

**响应格式**:
```typescript
interface FeishuFileUploadResponse {
    code: number;
    msg: string;
    data: {
        file_token: string;
    };
}
```

### 2. 导入为文档

**API端点**: `POST https://open.feishu.cn/open-apis/docx/v1/documents/import`  
**方法**: `importToDocument(fileToken: string, folderId?: string)`

**请求参数**:
```typescript
{
    file_extension: ".md",
    file_token: string,
    type: "docx",
    folder_token?: string   // 可选，目标文件夹
}
```

**响应格式**:
```typescript
interface FeishuDocCreateResponse {
    code: number;
    msg: string;
    data: {
        document: {
            document_id: string;
            revision_id: number;
            title: string;
        };
    };
}
```

### 3. 获取文档块列表

**API端点**: `GET https://open.feishu.cn/open-apis/docx/v1/documents/{document_id}/blocks`  
**方法**: `getDocumentBlocks(documentToken: string)`

**查询参数**:
```typescript
{
    page_size?: number,     // 默认500
    page_token?: string,    // 分页token
    document_revision_id?: number
}
```

**响应格式**:
```typescript
interface FeishuDocBlocksResponse {
    code: number;
    msg: string;
    data: {
        items: Array<{
            block_id: string;
            block_type: number;
            parent_id: string;
            children: string[];
            text?: {
                elements: Array<{
                    text_run?: {
                        content: string;
                    };
                }>;
            };
        }>;
        has_more: boolean;
        page_token: string;
    };
}
```

### 4. 创建文档块

**API端点**: `POST https://open.feishu.cn/open-apis/docx/v1/documents/{document_id}/blocks/{block_id}/children`  
**方法**: `createDocumentBlock(documentToken: string, blockId: string, blockData: any)`

**请求参数**:
```typescript
{
    children: Array<{
        block_type: number,     // 块类型
        file?: {
            file_token: string,
            name?: string
        },
        // 其他块特定属性
    }>,
    index?: number             // 插入位置
}
```

## 📁 文件和文件夹API

### 1. 上传本地文件

**API端点**: `POST https://open.feishu.cn/open-apis/drive/v1/files/upload_all`  
**方法**: `uploadLocalFile(file: TFile)`

**支持的文件类型**:
- 图片: PNG, JPG, JPEG, GIF, BMP, WEBP
- 文档: PDF, DOC, DOCX, PPT, PPTX, XLS, XLSX
- 其他: TXT, MD, CSV等

**文件大小限制**: 100MB

**请求格式**: `multipart/form-data`
```typescript
{
    file_name: string,
    parent_type: "bitable" | "doc" | "docx",
    parent_node: string,    // 文档token
    size: number,
    file: ArrayBuffer
}
```

### 2. 获取文件夹列表

**API端点**: `GET https://open.feishu.cn/open-apis/drive/v1/files`  
**方法**: `getFolderList(folderToken?: string)`

**查询参数**:
```typescript
{
    page_size?: number,     // 默认200
    page_token?: string,
    folder_token?: string,  // 父文件夹token，空为根目录
    order_by?: "EditedTime" | "CreatedTime",
    direction?: "ASC" | "DESC"
}
```

**响应格式**:
```typescript
interface FeishuFolderListResponse {
    code: number;
    msg: string;
    data: {
        files: Array<{
            token: string;
            name: string;
            type: string;           // "folder" | "doc" | "docx" | "file"
            parent_token: string;
            url: string;
            created_time: string;
            modified_time: string;
        }>;
        has_more: boolean;
        page_token: string;
    };
}
```

## 🔗 链接分享API

### 1. 创建分享链接

**API端点**: `POST https://open.feishu.cn/open-apis/drive/v1/permissions/{token}/public`  
**方法**: `createShareLink(documentToken: string, permission: LinkSharePermission)`

**请求参数**:
```typescript
{
    external_access_entity: "anyone" | "tenant",
    security_entity: "anyone_can_view" | "anyone_can_edit" | "tenant_can_view" | "tenant_can_edit",
    comment_entity?: "anyone_can_comment" | "tenant_can_comment",
    share_entity?: "anyone_can_share" | "tenant_can_share",
    link_share_entity?: "tenant_editable" | "tenant_readable" | "anyone_editable" | "anyone_readable"
}
```

## ⚠️ 错误处理

### 1. 通用错误码

```typescript
export const FEISHU_ERROR_MESSAGES: Record<number, string> = {
    // 文件相关
    1061002: '参数错误，请检查文件格式和大小',
    1061005: '文件大小超出限制',
    1061006: '文件类型不支持',
    
    // 授权相关
    99991663: 'access_token 无效',
    99991664: 'access_token 已过期',
    99991665: 'refresh_token 无效',
    99991666: 'refresh_token 已过期',
    
    // 权限相关
    230001: '无权限访问',
    230002: '用户或应用无权限',
    
    // 频率限制
    19001: 'API调用频率超限',
    
    // 系统错误
    99991400: '请求参数错误',
    99991500: '服务器内部错误'
};
```

### 2. 错误处理策略

```typescript
async function handleApiError(error: any, operation: string): Promise<void> {
    const errorCode = error.response?.data?.code;
    const errorMsg = FEISHU_ERROR_MESSAGES[errorCode] || error.message;
    
    Debug.error(`${operation} failed:`, {
        code: errorCode,
        message: errorMsg,
        details: error.response?.data
    });
    
    // 特殊错误处理
    if (errorCode === 99991664) {
        // Token过期，尝试刷新
        await this.refreshAccessToken();
    }
    
    throw new Error(`${operation}失败: ${errorMsg}`);
}
```

### 3. 重试机制

```typescript
async function apiCallWithRetry<T>(
    apiCall: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000
): Promise<T> {
    for (let i = 0; i < maxRetries; i++) {
        try {
            return await apiCall();
        } catch (error) {
            if (i === maxRetries - 1) throw error;
            
            // 指数退避
            await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)));
        }
    }
    throw new Error('Max retries exceeded');
}
```

## 🔧 配置常量

### 1. API基础配置

```typescript
export const FEISHU_CONFIG = {
    BASE_URL: 'https://open.feishu.cn/open-apis',
    OAUTH_URL: 'https://open.feishu.cn/open-apis/authen/v1',
    CALLBACK_URL: 'https://md2feishu.xinqi.life/oauth-callback',
    
    // API端点
    ENDPOINTS: {
        AUTHORIZE: '/authen/v1/authorize',
        ACCESS_TOKEN: '/authen/v1/oidc/access_token',
        REFRESH_TOKEN: '/authen/v1/oidc/refresh_access_token',
        USER_INFO: '/authen/v1/user_info',
        UPLOAD_FILE: '/drive/v1/files/upload_all',
        IMPORT_DOC: '/docx/v1/documents/import',
        GET_BLOCKS: '/docx/v1/documents/{document_id}/blocks',
        CREATE_BLOCK: '/docx/v1/documents/{document_id}/blocks/{block_id}/children',
        LIST_FILES: '/drive/v1/files',
        CREATE_SHARE: '/drive/v1/permissions/{token}/public'
    },
    
    // 限制配置
    LIMITS: {
        MAX_FILE_SIZE: 100 * 1024 * 1024, // 100MB
        MAX_RETRIES: 3,
        REQUEST_TIMEOUT: 30000, // 30秒
        BATCH_SIZE: 10 // 批量处理大小
    }
};
```

### 2. 文件类型映射

```typescript
export const FILE_TYPE_MAPPING = {
    // 图片类型
    IMAGE_TYPES: ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp'],
    
    // 文档类型
    DOCUMENT_TYPES: ['.pdf', '.doc', '.docx', '.ppt', '.pptx', '.xls', '.xlsx'],
    
    // 文本类型
    TEXT_TYPES: ['.txt', '.md', '.csv'],
    
    // 飞书块类型
    BLOCK_TYPES: {
        TEXT: 2,
        HEADING1: 3,
        HEADING2: 4,
        HEADING3: 5,
        BULLET_LIST: 6,
        ORDERED_LIST: 7,
        CODE: 8,
        QUOTE: 9,
        EQUATION: 10,
        TODO: 11,
        BITABLE: 12,
        CALLOUT: 13,
        CHAT_CARD: 14,
        DIAGRAM: 15,
        DIVIDER: 16,
        FILE: 17,
        GRID: 18,
        GRID_COLUMN: 19,
        IFRAME: 20,
        IMAGE: 21,
        ISV: 22,
        MINDNOTE: 23,
        SHEET: 24,
        TABLE: 25,
        TABLE_CELL: 26,
        VIEW: 27
    }
};
```

---

**文档说明**: 本文档提供了完整的飞书API调用规范，便于AI编程时准确实现API集成  
**最后更新**: 2025-08-10  
**维护者**: AI编程团队
