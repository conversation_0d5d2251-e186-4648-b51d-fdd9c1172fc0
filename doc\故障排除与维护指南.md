# 故障排除与维护指南

## 📋 概述

本文档提供了 obsidian-feishu-direct 插件的完整故障排除方案和维护指南，特别针对AI编程场景进行优化。

## 🚨 常见问题诊断

### 1. 授权相关问题

#### 问题：授权失败
**症状**:
- 点击授权按钮无反应
- 浏览器显示"应用未找到"
- 回调URL处理失败

**诊断步骤**:
```typescript
// 1. 检查应用配置
Debug.log('App configuration check', {
    appId: this.settings.appId ? '已配置' : '未配置',
    appSecret: this.settings.appSecret ? '已配置' : '未配置',
    callbackUrl: this.settings.callbackUrl
});

// 2. 验证授权URL生成
const authUrl = this.feishuApi.generateAuthUrl();
Debug.log('Generated auth URL', { authUrl });

// 3. 检查回调URL格式
function validateCallbackUrl(url: string): boolean {
    try {
        const parsed = new URL(url);
        return parsed.searchParams.has('code');
    } catch {
        return false;
    }
}
```

**解决方案**:
1. 验证飞书应用配置
2. 检查回调地址设置
3. 确认应用权限范围
4. 重新生成App Secret

#### 问题：Token过期
**症状**:
- API调用返回99991664错误
- 自动刷新失败
- 需要重新授权

**自动修复机制**:
```typescript
async ensureValidTokenWithReauth(statusNotice?: Notice): Promise<boolean> {
    try {
        // 1. 检查当前token
        if (!this.settings.accessToken) {
            Debug.warn('No access token available');
            return false;
        }
        
        // 2. 尝试刷新token
        const refreshed = await this.refreshAccessToken();
        if (refreshed) {
            Debug.log('Token refreshed successfully');
            return true;
        }
        
        // 3. 刷新失败，提示重新授权
        Debug.warn('Token refresh failed, re-authorization required');
        if (statusNotice) {
            statusNotice.setMessage('🔄 授权已过期，请重新授权...');
        }
        
        return false;
        
    } catch (error) {
        Debug.error('Token validation failed', error);
        return false;
    }
}
```

### 2. 文件上传问题

#### 问题：文件上传失败
**症状**:
- 上传进度卡住
- 返回1061005错误（文件过大）
- 返回1061006错误（文件类型不支持）

**诊断代码**:
```typescript
async function diagnoseFileUpload(file: TFile): Promise<DiagnosisResult> {
    const diagnosis: DiagnosisResult = {
        issues: [],
        suggestions: []
    };
    
    // 1. 检查文件大小
    if (file.stat.size > FEISHU_CONFIG.LIMITS.MAX_FILE_SIZE) {
        diagnosis.issues.push(`文件过大: ${file.stat.size} > ${FEISHU_CONFIG.LIMITS.MAX_FILE_SIZE}`);
        diagnosis.suggestions.push('压缩文件或分割为多个小文件');
    }
    
    // 2. 检查文件类型
    const extension = file.extension.toLowerCase();
    const supportedTypes = [...FILE_TYPE_MAPPING.IMAGE_TYPES, ...FILE_TYPE_MAPPING.DOCUMENT_TYPES];
    if (!supportedTypes.includes(`.${extension}`)) {
        diagnosis.issues.push(`不支持的文件类型: ${extension}`);
        diagnosis.suggestions.push('转换为支持的文件格式');
    }
    
    // 3. 检查文件名
    if (file.name.includes('#') || file.name.includes('?')) {
        diagnosis.issues.push('文件名包含特殊字符');
        diagnosis.suggestions.push('重命名文件，移除特殊字符');
    }
    
    return diagnosis;
}
```

#### 问题：占位符替换失败
**症状**:
- 文档中显示占位符而非实际文件
- 部分文件未正确嵌入

**修复机制**:
```typescript
async function repairPlaceholders(documentToken: string, placeholders: PlaceholderBlock[]): Promise<void> {
    Debug.log('Starting placeholder repair', { count: placeholders.length });
    
    for (const placeholder of placeholders) {
        try {
            // 1. 验证占位符是否存在
            const blocks = await this.getDocumentBlocks(documentToken);
            const placeholderExists = this.findPlaceholderInBlocks(blocks, placeholder.placeholder);
            
            if (!placeholderExists) {
                Debug.warn('Placeholder not found in document', { placeholder: placeholder.placeholder });
                continue;
            }
            
            // 2. 重新上传文件
            const uploadResult = await this.uploadLocalFile(placeholder.file);
            if (!uploadResult.success) {
                Debug.error('File re-upload failed', { fileName: placeholder.file.name });
                continue;
            }
            
            // 3. 替换占位符
            await this.replacePlaceholderWithFile(documentToken, placeholder, uploadResult.fileToken);
            Debug.log('Placeholder repaired', { placeholder: placeholder.placeholder });
            
        } catch (error) {
            Debug.error('Placeholder repair failed', { 
                placeholder: placeholder.placeholder,
                error: error.message 
            });
        }
    }
}
```

### 3. Markdown处理问题

#### 问题：格式转换错误
**症状**:
- 双链语法未正确转换
- Callout块显示异常
- 代码块格式丢失

**调试工具**:
```typescript
function debugMarkdownProcessing(content: string): ProcessingDebugInfo {
    const debugInfo: ProcessingDebugInfo = {
        originalLength: content.length,
        steps: []
    };
    
    // 1. 记录原始内容
    debugInfo.steps.push({
        step: 'original',
        length: content.length,
        sample: content.substring(0, 100)
    });
    
    // 2. 逐步处理并记录
    let processed = content;
    
    processed = this.processWikiLinks(processed);
    debugInfo.steps.push({
        step: 'wikiLinks',
        length: processed.length,
        changes: this.countChanges(content, processed)
    });
    
    processed = this.processCallouts(processed);
    debugInfo.steps.push({
        step: 'callouts',
        length: processed.length,
        changes: this.countChanges(content, processed)
    });
    
    // ... 其他处理步骤
    
    return debugInfo;
}
```

### 4. 网络连接问题

#### 问题：API调用超时
**症状**:
- 请求长时间无响应
- 间歇性连接失败

**重试机制**:
```typescript
async function apiCallWithRetry<T>(
    apiCall: () => Promise<T>,
    options: RetryOptions = {}
): Promise<T> {
    const {
        maxRetries = 3,
        baseDelay = 1000,
        maxDelay = 10000,
        backoffFactor = 2
    } = options;
    
    let lastError: Error;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
        try {
            Debug.log(`API call attempt ${attempt + 1}/${maxRetries + 1}`);
            return await apiCall();
            
        } catch (error) {
            lastError = error;
            
            if (attempt === maxRetries) {
                Debug.error('All retry attempts failed', { 
                    attempts: attempt + 1,
                    lastError: error.message 
                });
                break;
            }
            
            // 计算延迟时间（指数退避）
            const delay = Math.min(
                baseDelay * Math.pow(backoffFactor, attempt),
                maxDelay
            );
            
            Debug.warn(`API call failed, retrying in ${delay}ms`, {
                attempt: attempt + 1,
                error: error.message
            });
            
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }
    
    throw lastError;
}
```

## 🔧 维护任务

### 1. 定期维护检查

#### 每日检查清单
```typescript
async function dailyHealthCheck(): Promise<HealthCheckResult> {
    const result: HealthCheckResult = {
        timestamp: new Date().toISOString(),
        checks: []
    };
    
    // 1. 检查授权状态
    try {
        const userInfo = await this.feishuApi.getUserInfo();
        result.checks.push({
            name: 'auth_status',
            status: 'healthy',
            message: `用户: ${userInfo.name}`
        });
    } catch (error) {
        result.checks.push({
            name: 'auth_status',
            status: 'error',
            message: `授权检查失败: ${error.message}`
        });
    }
    
    // 2. 检查API连通性
    try {
        await this.feishuApi.getFolderList();
        result.checks.push({
            name: 'api_connectivity',
            status: 'healthy',
            message: 'API连接正常'
        });
    } catch (error) {
        result.checks.push({
            name: 'api_connectivity',
            status: 'error',
            message: `API连接失败: ${error.message}`
        });
    }
    
    // 3. 检查本地配置
    const configValid = this.validateConfiguration();
    result.checks.push({
        name: 'configuration',
        status: configValid ? 'healthy' : 'warning',
        message: configValid ? '配置正常' : '配置存在问题'
    });
    
    return result;
}
```

#### 每周维护任务
```typescript
async function weeklyMaintenance(): Promise<void> {
    Debug.log('Starting weekly maintenance');
    
    // 1. 清理过期缓存
    this.clearExpiredCache();
    
    // 2. 检查插件更新
    await this.checkForUpdates();
    
    // 3. 优化本地存储
    await this.optimizeLocalStorage();
    
    // 4. 生成使用统计
    const stats = await this.generateUsageStats();
    Debug.log('Weekly stats', stats);
    
    Debug.log('Weekly maintenance completed');
}
```

### 2. 性能监控

#### 性能指标收集
```typescript
class PerformanceMonitor {
    private metrics: Map<string, PerformanceMetric[]> = new Map();
    
    startTimer(operation: string): string {
        const timerId = `${operation}_${Date.now()}_${Math.random()}`;
        const metric: PerformanceMetric = {
            operation,
            startTime: performance.now(),
            timerId
        };
        
        if (!this.metrics.has(operation)) {
            this.metrics.set(operation, []);
        }
        this.metrics.get(operation)!.push(metric);
        
        return timerId;
    }
    
    endTimer(timerId: string): number {
        for (const [operation, metrics] of this.metrics.entries()) {
            const metric = metrics.find(m => m.timerId === timerId);
            if (metric) {
                metric.endTime = performance.now();
                metric.duration = metric.endTime - metric.startTime;
                
                Debug.log(`Performance: ${operation}`, {
                    duration: `${metric.duration.toFixed(2)}ms`
                });
                
                return metric.duration;
            }
        }
        return 0;
    }
    
    getAverageTime(operation: string): number {
        const metrics = this.metrics.get(operation) || [];
        const completedMetrics = metrics.filter(m => m.duration !== undefined);
        
        if (completedMetrics.length === 0) return 0;
        
        const total = completedMetrics.reduce((sum, m) => sum + m.duration!, 0);
        return total / completedMetrics.length;
    }
}
```

### 3. 数据备份和恢复

#### 配置备份
```typescript
async function backupConfiguration(): Promise<ConfigBackup> {
    const backup: ConfigBackup = {
        timestamp: new Date().toISOString(),
        version: this.manifest.version,
        settings: {
            // 只备份非敏感配置
            titleSource: this.settings.titleSource,
            frontMatterHandling: this.settings.frontMatterHandling,
            defaultFolderName: this.settings.defaultFolderName,
            enableLinkShare: this.settings.enableLinkShare,
            enableSubDocumentUpload: this.settings.enableSubDocumentUpload,
            enableLocalImageUpload: this.settings.enableLocalImageUpload,
            enableLocalAttachmentUpload: this.settings.enableLocalAttachmentUpload
        }
    };
    
    // 保存到本地文件
    const backupPath = `${this.app.vault.configDir}/plugins/feishu-backup-${Date.now()}.json`;
    await this.app.vault.adapter.write(backupPath, JSON.stringify(backup, null, 2));
    
    Debug.log('Configuration backed up', { path: backupPath });
    return backup;
}

async function restoreConfiguration(backupPath: string): Promise<void> {
    try {
        const backupContent = await this.app.vault.adapter.read(backupPath);
        const backup: ConfigBackup = JSON.parse(backupContent);
        
        // 恢复配置（排除敏感信息）
        Object.assign(this.settings, backup.settings);
        await this.saveSettings();
        
        Debug.log('Configuration restored', { 
            version: backup.version,
            timestamp: backup.timestamp 
        });
        
    } catch (error) {
        Debug.error('Configuration restore failed', error);
        throw new Error(`配置恢复失败: ${error.message}`);
    }
}
```

## 🚨 紧急故障处理

### 1. 服务完全不可用

**应急响应流程**:
```typescript
async function emergencyRecovery(): Promise<RecoveryResult> {
    Debug.log('Starting emergency recovery');
    
    const recovery: RecoveryResult = {
        steps: [],
        success: false
    };
    
    try {
        // 1. 重置授权状态
        recovery.steps.push('重置授权状态');
        this.settings.accessToken = '';
        this.settings.refreshToken = '';
        this.settings.userInfo = null;
        await this.saveSettings();
        
        // 2. 清理缓存
        recovery.steps.push('清理本地缓存');
        localStorage.removeItem('feishu-oauth-state');
        
        // 3. 重新初始化服务
        recovery.steps.push('重新初始化服务');
        this.feishuApi = new FeishuApiService(this.settings, this.app);
        
        // 4. 测试基本功能
        recovery.steps.push('测试基本功能');
        const authUrl = this.feishuApi.generateAuthUrl();
        if (!authUrl) {
            throw new Error('无法生成授权链接');
        }
        
        recovery.success = true;
        recovery.message = '紧急恢复完成，请重新授权';
        
    } catch (error) {
        recovery.success = false;
        recovery.error = error.message;
        recovery.message = '紧急恢复失败，请联系技术支持';
    }
    
    Debug.log('Emergency recovery completed', recovery);
    return recovery;
}
```

### 2. 数据丢失恢复

**数据恢复策略**:
```typescript
async function recoverLostData(documentId: string): Promise<RecoveryResult> {
    try {
        // 1. 尝试从飞书恢复文档
        const document = await this.feishuApi.getDocument(documentId);
        if (document) {
            return {
                success: true,
                message: '文档已从飞书恢复',
                data: document
            };
        }
        
        // 2. 检查本地备份
        const localBackup = await this.findLocalBackup(documentId);
        if (localBackup) {
            return {
                success: true,
                message: '文档已从本地备份恢复',
                data: localBackup
            };
        }
        
        // 3. 尝试从历史记录恢复
        const historyData = await this.recoverFromHistory(documentId);
        if (historyData) {
            return {
                success: true,
                message: '文档已从历史记录恢复',
                data: historyData
            };
        }
        
        return {
            success: false,
            message: '无法恢复数据，所有恢复方案均失败'
        };
        
    } catch (error) {
        return {
            success: false,
            message: `数据恢复失败: ${error.message}`
        };
    }
}
```

## 📊 监控和告警

### 1. 关键指标监控

```typescript
interface MonitoringMetrics {
    // 性能指标
    averageUploadTime: number;
    successRate: number;
    errorRate: number;
    
    // 使用指标
    dailyUploads: number;
    totalDocuments: number;
    activeUsers: number;
    
    // 系统指标
    memoryUsage: number;
    apiCallCount: number;
    cacheHitRate: number;
}

async function collectMetrics(): Promise<MonitoringMetrics> {
    return {
        averageUploadTime: this.performanceMonitor.getAverageTime('file_upload'),
        successRate: this.calculateSuccessRate(),
        errorRate: this.calculateErrorRate(),
        dailyUploads: this.getDailyUploadCount(),
        totalDocuments: this.getTotalDocumentCount(),
        activeUsers: this.getActiveUserCount(),
        memoryUsage: this.getMemoryUsage(),
        apiCallCount: this.getApiCallCount(),
        cacheHitRate: this.getCacheHitRate()
    };
}
```

### 2. 自动告警

```typescript
function checkAlerts(metrics: MonitoringMetrics): Alert[] {
    const alerts: Alert[] = [];
    
    // 错误率过高
    if (metrics.errorRate > 0.1) {
        alerts.push({
            level: 'critical',
            message: `错误率过高: ${(metrics.errorRate * 100).toFixed(1)}%`,
            action: '检查API连接和授权状态'
        });
    }
    
    // 上传时间过长
    if (metrics.averageUploadTime > 30000) {
        alerts.push({
            level: 'warning',
            message: `上传时间过长: ${metrics.averageUploadTime.toFixed(0)}ms`,
            action: '检查网络连接和文件大小'
        });
    }
    
    // 内存使用过高
    if (metrics.memoryUsage > 100 * 1024 * 1024) {
        alerts.push({
            level: 'warning',
            message: `内存使用过高: ${(metrics.memoryUsage / 1024 / 1024).toFixed(1)}MB`,
            action: '清理缓存和优化内存使用'
        });
    }
    
    return alerts;
}
```

---

**文档说明**: 本指南提供了完整的故障排除和维护方案，确保插件稳定运行  
**最后更新**: 2025-08-10  
**维护者**: AI编程团队
