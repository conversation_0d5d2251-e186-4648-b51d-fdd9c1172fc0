# 开发规范与最佳实践

## 📋 概述

本文档为 obsidian-feishu-direct 插件的开发规范，特别针对AI编程场景进行优化，确保代码质量、安全性和可维护性。

## 🎯 核心开发原则

### 1. KISS原则 (Keep It Simple, Stupid)
- 永远选择最简单、最直接的有效解决方案
- 避免过度设计和不必要的复杂性
- 优先使用标准库和成熟的解决方案

### 2. YAGNI原则 (You Aren't Gonna Need It)
- 绝不实现当前需求之外的任何功能
- 抵制为"未来可能"的扩展性而增加当前复杂度
- 专注于当前明确的需求

### 3. SOLID原则
- **单一职责**: 每个类/函数只负责一个明确的功能
- **开闭原则**: 对扩展开放，对修改封闭
- **里氏替换**: 子类可以替换父类
- **接口隔离**: 使用多个专门的接口
- **依赖倒置**: 依赖抽象而非具体实现

## 📝 代码质量规范

### 1. 文件和函数长度限制

```typescript
// ✅ 正确：单个文件不超过1000行
// ❌ 错误：超过1000行的巨型文件

// ✅ 正确：单个函数不超过50行
function processMarkdown(content: string): string {
    // 实现逻辑...
    return processedContent;
}

// ❌ 错误：超过50行的巨型函数
```

### 2. 命名规范

```typescript
// ✅ 正确：有意义的命名
const userAccessToken = 'xxx';
const feishuApiService = new FeishuApiService();
function uploadMarkdownFile(title: string, content: string) {}

// ❌ 错误：模糊的命名
const a = 'xxx';
const temp = new Service();
function process(data: any) {}
```

### 3. 注释规范

```typescript
/**
 * 上传Markdown文件到飞书
 * @param title 文档标题
 * @param content Markdown内容
 * @returns 上传结果，包含文件token
 */
async uploadMarkdownFile(title: string, content: string): Promise<UploadResult> {
    // 验证输入参数
    if (!title || !content) {
        throw new Error('标题和内容不能为空');
    }
    
    // 构建文件数据
    const fileData = this.buildFileData(title, content);
    
    // 执行上传请求
    return await this.executeUpload(fileData);
}
```

### 4. 类型安全

```typescript
// ✅ 正确：完整的类型定义
interface FeishuApiResponse<T> {
    code: number;
    msg: string;
    data: T;
}

interface UploadResult {
    success: boolean;
    fileToken?: string;
    error?: string;
}

// ❌ 错误：使用any类型
function processData(data: any): any {
    return data;
}
```

## 🛡️ 安全规范

### 1. 输入验证

```typescript
// ✅ 正确：严格的输入验证
function sanitizeInput(input: string): string {
    if (typeof input !== 'string') {
        throw new Error('输入必须是字符串');
    }
    
    return input
        .trim()
        .replace(/[<>]/g, '') // 移除潜在的HTML标签
        .substring(0, 1000);   // 限制长度
}

// URL验证
function validateUrl(url: string): boolean {
    try {
        const parsed = new URL(url);
        return ['http:', 'https:'].includes(parsed.protocol);
    } catch {
        return false;
    }
}
```

### 2. 敏感信息处理

```typescript
// ✅ 正确：安全的日志记录
Debug.log('API call completed', {
    endpoint: '/api/upload',
    status: 'success',
    // 绝不记录敏感信息
});

// ❌ 错误：泄露敏感信息
Debug.log('User data:', {
    accessToken: 'xxx',  // 危险！
    password: 'xxx'      // 危险！
});
```

### 3. DOM操作安全

```typescript
// ✅ 正确：安全的DOM操作
export class DomUtils {
    static setContent(element: HTMLElement, content: string) {
        element.textContent = content; // 使用textContent而非innerHTML
    }
    
    static createElementWithText(tag: string, text: string): HTMLElement {
        const element = document.createElement(tag);
        element.textContent = text;
        return element;
    }
}

// ❌ 错误：不安全的DOM操作
element.innerHTML = userInput; // XSS风险
```

## 🔧 错误处理规范

### 1. 全面的错误处理

```typescript
// ✅ 正确：完整的错误处理
async function uploadFile(file: TFile): Promise<UploadResult> {
    try {
        // 验证文件
        if (!file) {
            throw new Error('文件不能为空');
        }
        
        if (file.stat.size > MAX_FILE_SIZE) {
            throw new Error(`文件大小超出限制: ${file.stat.size} > ${MAX_FILE_SIZE}`);
        }
        
        // 执行上传
        const result = await this.performUpload(file);
        
        Debug.log('File uploaded successfully', {
            fileName: file.name,
            size: file.stat.size
        });
        
        return { success: true, fileToken: result.fileToken };
        
    } catch (error) {
        Debug.error('File upload failed', {
            fileName: file?.name,
            error: error.message
        });
        
        return { 
            success: false, 
            error: `文件上传失败: ${error.message}` 
        };
    }
}
```

### 2. 错误分类和处理

```typescript
// 定义错误类型
export enum ErrorType {
    NETWORK_ERROR = 'NETWORK_ERROR',
    AUTH_ERROR = 'AUTH_ERROR',
    VALIDATION_ERROR = 'VALIDATION_ERROR',
    API_ERROR = 'API_ERROR'
}

export class FeishuError extends Error {
    constructor(
        public type: ErrorType,
        public code: number,
        message: string,
        public details?: any
    ) {
        super(message);
        this.name = 'FeishuError';
    }
}

// 使用示例
function handleApiError(error: any): never {
    if (error.response?.status === 401) {
        throw new FeishuError(
            ErrorType.AUTH_ERROR,
            401,
            '授权失效，请重新登录',
            error.response.data
        );
    }
    
    throw new FeishuError(
        ErrorType.API_ERROR,
        error.response?.status || 500,
        error.message,
        error.response?.data
    );
}
```

## 🚀 性能优化规范

### 1. 异步操作优化

```typescript
// ✅ 正确：并发处理
async function uploadMultipleFiles(files: TFile[]): Promise<UploadResult[]> {
    // 批量并发上传，但限制并发数
    const BATCH_SIZE = 5;
    const results: UploadResult[] = [];
    
    for (let i = 0; i < files.length; i += BATCH_SIZE) {
        const batch = files.slice(i, i + BATCH_SIZE);
        const batchResults = await Promise.all(
            batch.map(file => this.uploadFile(file))
        );
        results.push(...batchResults);
    }
    
    return results;
}

// ❌ 错误：串行处理
async function uploadMultipleFilesSerial(files: TFile[]): Promise<UploadResult[]> {
    const results: UploadResult[] = [];
    for (const file of files) {
        results.push(await this.uploadFile(file)); // 效率低下
    }
    return results;
}
```

### 2. 内存管理

```typescript
// ✅ 正确：及时释放资源
async function processLargeFile(file: TFile): Promise<void> {
    let fileContent: ArrayBuffer | null = null;
    
    try {
        fileContent = await this.app.vault.readBinary(file);
        await this.processContent(fileContent);
    } finally {
        // 及时释放内存
        fileContent = null;
    }
}
```

### 3. 缓存策略

```typescript
// 简单的内存缓存
class SimpleCache<T> {
    private cache = new Map<string, { data: T; timestamp: number }>();
    private ttl: number;
    
    constructor(ttlMs: number = 5 * 60 * 1000) { // 默认5分钟
        this.ttl = ttlMs;
    }
    
    get(key: string): T | null {
        const item = this.cache.get(key);
        if (!item) return null;
        
        if (Date.now() - item.timestamp > this.ttl) {
            this.cache.delete(key);
            return null;
        }
        
        return item.data;
    }
    
    set(key: string, data: T): void {
        this.cache.set(key, { data, timestamp: Date.now() });
    }
}
```

## 🔍 调试和日志规范

### 1. 调试工具使用

```typescript
// ✅ 正确：使用统一的调试工具
export class Debug {
    private static enabled = false; // 生产环境禁用
    
    static log(message: string, data?: any): void {
        if (this.enabled) {
            console.log(`[Feishu] ${message}`, data || '');
        }
    }
    
    static warn(message: string, data?: any): void {
        if (this.enabled) {
            console.warn(`[Feishu] ${message}`, data || '');
        }
    }
    
    static error(message: string, data?: any): void {
        if (this.enabled) {
            console.error(`[Feishu] ${message}`, data || '');
        }
    }
}

// 使用示例
Debug.log('Starting file upload', { fileName: file.name });
Debug.error('Upload failed', { error: error.message });
```

### 2. 关键路径日志

```typescript
async function shareToFeishu(file: TFile): Promise<ShareResult> {
    Debug.log('=== 开始分享流程 ===', { fileName: file.name });
    
    try {
        Debug.log('1. 检查授权状态');
        await this.ensureValidToken();
        
        Debug.log('2. 读取文件内容');
        const content = await this.app.vault.read(file);
        
        Debug.log('3. 处理Markdown内容');
        const processResult = this.markdownProcessor.processCompleteWithFiles(content);
        
        Debug.log('4. 上传到飞书', { 
            localFilesCount: processResult.localFiles.length 
        });
        const result = await this.feishuApi.shareMarkdownWithFiles(title, processResult);
        
        Debug.log('=== 分享完成 ===', { success: result.success });
        return result;
        
    } catch (error) {
        Debug.error('=== 分享失败 ===', { error: error.message });
        throw error;
    }
}
```

## 🧪 测试规范

### 1. 单元测试结构

```typescript
// 测试文件命名: *.test.ts
describe('MarkdownProcessor', () => {
    let processor: MarkdownProcessor;
    
    beforeEach(() => {
        processor = new MarkdownProcessor(mockApp);
    });
    
    describe('processWikiLinks', () => {
        it('should convert wiki links to markdown links', () => {
            const input = '[[Note Title]]';
            const expected = '[Note Title](Note%20Title.md)';
            const result = processor.processWikiLinks(input);
            expect(result).toBe(expected);
        });
        
        it('should handle wiki links with display text', () => {
            const input = '[[Note Title|Display Text]]';
            const expected = '[Display Text](Note%20Title.md)';
            const result = processor.processWikiLinks(input);
            expect(result).toBe(expected);
        });
    });
});
```

### 2. 集成测试

```typescript
describe('FeishuApiService Integration', () => {
    let apiService: FeishuApiService;
    
    beforeEach(() => {
        apiService = new FeishuApiService(mockSettings, mockApp);
    });
    
    it('should upload and import markdown file', async () => {
        const title = 'Test Document';
        const content = '# Hello World\n\nThis is a test.';
        
        const result = await apiService.shareMarkdown(title, content);
        
        expect(result.success).toBe(true);
        expect(result.url).toBeDefined();
        expect(result.title).toBe(title);
    });
});
```

## 📦 模块化设计规范

### 1. 模块职责划分

```typescript
// 每个模块都有明确的单一职责

// feishu-api.ts - 专门处理API调用
export class FeishuApiService {
    // 只负责API通信，不处理业务逻辑
}

// markdown-processor.ts - 专门处理Markdown转换
export class MarkdownProcessor {
    // 只负责内容转换，不涉及API调用
}

// settings.ts - 专门处理设置界面
export class FeishuSettingTab {
    // 只负责UI展示和用户交互
}
```

### 2. 依赖注入

```typescript
// ✅ 正确：通过构造函数注入依赖
export class FeishuPlugin extends Plugin {
    private feishuApi: FeishuApiService;
    private markdownProcessor: MarkdownProcessor;
    
    async onload(): Promise<void> {
        await this.loadSettings();
        
        // 注入依赖
        this.feishuApi = new FeishuApiService(this.settings, this.app);
        this.markdownProcessor = new MarkdownProcessor(this.app);
    }
}
```

### 3. 接口定义

```typescript
// 定义清晰的接口契约
export interface IMarkdownProcessor {
    process(content: string): string;
    processCompleteWithFiles(content: string, ...options: any[]): MarkdownProcessResult;
}

export interface IFeishuApiService {
    shareMarkdown(title: string, content: string): Promise<ShareResult>;
    uploadFile(file: TFile): Promise<UploadResult>;
}
```

## 🔄 版本控制规范

### 1. 提交信息格式

```
type(scope): description

feat(api): 添加文件上传功能
fix(auth): 修复token刷新问题
docs(readme): 更新安装说明
refactor(processor): 重构Markdown处理逻辑
test(api): 添加API服务单元测试
```

### 2. 分支管理

```
main        - 生产版本
develop     - 开发版本
feature/*   - 功能分支
hotfix/*    - 紧急修复
release/*   - 发布分支
```

## 📋 代码审查清单

### 提交前检查清单

- [ ] 代码符合命名规范
- [ ] 函数长度不超过50行
- [ ] 文件长度不超过1000行
- [ ] 所有函数都有JSDoc注释
- [ ] 错误处理完整
- [ ] 没有使用innerHTML
- [ ] 没有硬编码的魔法数字
- [ ] 敏感信息已脱敏
- [ ] 调试日志使用Debug类
- [ ] 类型定义完整
- [ ] 单元测试覆盖核心逻辑

### 安全检查清单

- [ ] 输入验证完整
- [ ] 没有XSS风险
- [ ] 敏感信息不在日志中
- [ ] API调用有超时设置
- [ ] 错误信息不泄露内部信息
- [ ] 文件上传有大小限制
- [ ] URL验证正确

---

**文档说明**: 本规范为AI编程优化，确保代码质量和安全性  
**最后更新**: 2025-08-10  
**维护者**: AI编程团队
