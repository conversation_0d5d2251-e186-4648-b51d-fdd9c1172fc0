# 自动分享标记功能说明

## 🎯 功能概述

新增的"自动分享标记"功能可以在分享成功后，自动在笔记的 Front Matter 中添加分享相关的标记信息，帮助用户追踪和管理已分享的内容。

## ✨ 功能特点

### 自动标记
- ✅ 分享成功后自动添加 `feishushare: true` 标记
- 🔗 记录分享链接 `feishu_url`
- ⏰ 记录分享时间 `feishu_shared_at`
- 🔄 重复分享时自动更新时间戳
- ⚙️ 可在设置中开启/关闭此功能

### 安全机制
- 🛡️ 只有获取到分享URL才会添加标记
- 🔒 分享失败时不会添加任何标记
- 📝 标记添加失败不影响主要分享流程

## 🚀 使用方法

### 1. 启用功能
1. 打开 Obsidian 设置
2. 进入"飞书分享"插件设置
3. 在"内容处理设置"部分找到"自动添加分享标记"
4. 开启此开关

### 2. 分享笔记
按照正常流程分享笔记到飞书：
- 右键菜单 → "📤 分享到飞书"
- 命令面板 → "分享当前笔记到飞书"
- 文件管理器右键 → "📤 分享到飞书"

### 3. 查看结果
分享成功后，笔记顶部会自动添加类似这样的 Front Matter：

```yaml
---
feishushare: true
feishu_url: "https://example.feishu.cn/docs/xxx"
feishu_shared_at: "2024-01-10T10:30:00.000Z"
---
```

## 📋 字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `feishushare` | boolean | 标记文档是否已分享到飞书 |
| `feishu_url` | string | 分享后的飞书文档链接 |
| `feishu_shared_at` | string | 分享时间（ISO 8601 格式） |

## 🔄 重复分享行为

当对已有分享标记的文档再次分享时：
- ✅ 保持 `feishushare: true`
- 🔗 更新 `feishu_url`（如果链接发生变化）
- ⏰ 更新 `feishu_shared_at` 为最新分享时间

## 💡 使用场景

### 1. 分享状态追踪
快速识别哪些笔记已经分享过：
```yaml
---
feishushare: true
---
```

### 2. 链接管理
直接从 Front Matter 获取分享链接：
```yaml
---
feishu_url: "https://example.feishu.cn/docs/xxx"
---
```

### 3. 时间记录
了解文档的分享历史：
```yaml
---
feishu_shared_at: "2024-01-10T10:30:00.000Z"
---
```

### 4. 批量处理
使用 Obsidian 的搜索功能查找所有已分享的文档：
```
feishushare:true
```

## ⚠️ 注意事项

1. **功能开关**：此功能默认关闭，需要手动在设置中启用
2. **URL 依赖**：只有成功获取到分享链接才会添加标记
3. **格式兼容**：会保持现有 Front Matter 的其他字段不变
4. **错误处理**：标记添加失败不会影响分享成功的主流程

## 🔧 故障排除

### 分享成功但没有添加标记
1. 检查设置中是否启用了"自动添加分享标记"
2. 确认分享是否真的成功并获取到了URL
3. 查看控制台是否有相关错误信息

### Front Matter 格式异常
1. 插件会自动处理特殊字符的转义
2. 如果出现格式问题，可以手动调整
3. 重新分享会覆盖之前的标记

## 🎉 总结

自动分享标记功能让您可以：
- 📊 更好地管理和追踪已分享的内容
- 🔗 快速访问分享链接
- ⏰ 了解分享历史
- 🔍 批量查找已分享的文档

这个功能完全可选，不会影响现有的分享流程，为需要更精细管理的用户提供了额外的便利。
