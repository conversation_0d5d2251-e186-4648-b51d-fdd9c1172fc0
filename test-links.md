# 链接处理测试

这个文档用来测试不同类型链接的处理方式。

## 1. Obsidian协议链接
ob地址：[笔记](obsidian://open?vault=PKM&file=01989328_9962_76cf_8500_e5d362110ce0)

## 2. 普通网络链接
[baidu](www.baidu.com)
[Google](https://www.google.com)

## 3. 双链（Wiki链接）
[[test-subdoc]]
[[test-subdoc|子文档别名]]

## 4. 文件嵌入
![[test-subdoc.md]]

## 5. 图片链接
![网络图片](https://example.com/image.jpg)
![本地图片](./local-image.png)

## 6. 块引用
[[test-subdoc#^block123]]

## 预期处理结果

根据当前代码分析：

### Obsidian协议链接
- **当前处理**：保持原样，不做任何转换
- **结果**：`[笔记](obsidian://open?vault=PKM&file=01989328_9962_76cf_8500_e5d362110ce0)`

### 普通网络链接  
- **当前处理**：保持原样，不做任何转换
- **结果**：`[baidu](www.baidu.com)` 和 `[Google](https://www.google.com)`

### 双链（Wiki链接）
- **当前处理**：
  - 如果链接到存在的MD文件 → 转换为子文档占位符（如果启用子文档上传）
  - 如果链接到不存在的文件 → 转换为 `📝 链接文本`
- **结果**：
  - `[[test-subdoc]]` → 子文档占位符或 `📝 test-subdoc`
  - `[[test-subdoc|子文档别名]]` → 子文档占位符或 `📝 子文档别名`

### 文件嵌入
- **当前处理**：
  - MD文件 → 作为子文档处理
  - 图片/附件 → 生成占位符，上传到飞书
- **结果**：`![[test-subdoc.md]]` → 子文档占位符

### 图片链接
- **当前处理**：
  - 网络图片 → 保持原样
  - 本地图片 → 生成占位符，上传到飞书
- **结果**：
  - `![网络图片](https://example.com/image.jpg)` → 保持原样
  - `![本地图片](./local-image.png)` → 占位符

### 块引用
- **当前处理**：转换为文本描述
- **结果**：`[[test-subdoc#^block123]]` → `📝 test-subdoc (块引用: block123)`
